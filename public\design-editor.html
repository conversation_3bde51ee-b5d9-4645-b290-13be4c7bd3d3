<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Design Canvas Studio - Image & Text</title>
    <link rel="stylesheet" href="/styles.css"> <!-- Main styles with proper topbar -->
    <link rel="stylesheet" href="/style-design-editor.css">
    <link href="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/themes/nano.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"> <!-- Added Font Awesome -->
    <link rel="stylesheet" href="/css/artboard-edit.css"> <!-- Artboard Edit Mode Styles -->
    <link rel="stylesheet" href="/css/left-menu.css"> <!-- Left Menu Styles -->
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,500;0,600;1,400&family=Arial&family=Verdana&family=Georgia&family=Times+New+Roman&family=Courier+New&family=Impact&family=Comic+Sans+MS:wght@400;700&display=swap" rel="stylesheet">

    <!-- Professional Button Styles -->
    <style>
        /* Professional buttons matching collection page style */
        .professional-btn {
            width: calc(70% - 20px);
            margin-left: 110px;
            padding: 0.6rem 1rem;
            font-size: 0.9em;
            font-family: 'Arial', sans-serif;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            background: white;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 8px;
            margin-bottom: 8px;
            text-decoration: none;
            text-align: left;
            font-style: normal;
        }

        .professional-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .professional-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .professional-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .professional-btn:disabled:hover {
            background: white;
            border-color: #d1d5db;
            transform: none;
            box-shadow: none;
        }

        .professional-btn i {
            font-size: 0.85em;
            color: #6b7280;
        }

        /* Image actions container */
        .image-actions-container {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        /* Header styling for sections */
        .image-actions-header {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 0.95rem;
            color: #374151;
            margin-top: 20px;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        /* AI Object Enhancement Container Styling */
        .ai-object-enhancement-container {
            display: flex;
            gap: 6px;
            align-items: center;
            margin-top: 6px;
        }

        .ai-tone-dropdown {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 12px;
            cursor: pointer;
            color: #374151;
            min-width: 0; /* Allow shrinking */
        }

        .ai-tone-dropdown:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
        }

        .ai-enhance-object-btn {
            background: #ffffff;
            color: #393939;
            border: 1px solid #ddd;
            padding: 6px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 0 0 32px;
            height: 32px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .ai-enhance-object-btn:hover {
            background: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        .ai-enhance-object-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .ai-enhance-object-btn.regenerate-mode {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .ai-enhance-object-btn.regenerate-mode:hover {
            background: #5a6268;
            border-color: #545b62;
        }

        /* AI Enhancement Section Styling */
        .ai-enhancement-section {
            margin-left: 115px; /* Align with text input field */
            max-width: calc(100% - 80px);
            overflow: visible;
            position: relative;
            z-index: 100;
        }

        .ai-enhancement-layout {
            display: flex;
            gap: 6px;
            align-items: center;
            max-width: 100%;
            overflow: visible;
            position: relative;
        }

        .ai-checkbox-row {
            margin-top: 10px;
        }
        .ai-checkbox-row-side {
            margin-top: 10px;
            margin-bottom: 20px;
        }

        .text-tone-dropdown-full {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
            font-size: 12px;
            cursor: pointer;
            color: #374151;
            box-sizing: border-box;
            position: relative;
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .text-tone-dropdown:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
        }

        .text-tone-dropdown:disabled {
            background-color: #f3f4f6;
            color: #9ca3af;
            cursor: not-allowed;
        }

        .enhance-text-btn {
            background: #ffffff;
            color: #393939;
            border: 1px solid #d1d5db;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 3px;
            white-space: nowrap;
            transition: all 0.2s ease;
            font-family: inherit;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .enhance-text-btn:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        .enhance-text-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        .enhance-text-btn.regenerate-mode {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .enhance-text-btn.regenerate-mode:hover {
            background: #5a6268;
            border-color: #545b62;
        }

        /* Square button for AI enhancement */
        .enhance-text-btn-square {
            background: #ffffff;
            color: #393939;
            border: 1px solid #d1d5db;
            padding: 6px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            font-family: inherit;
            width: 32px;
            height: 32px;
            min-width: 32px;
            box-sizing: border-box;
        }

        .enhance-text-btn-square:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #007bff;
            color: #007bff;
        }

        .enhance-text-btn-square:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            background-color: #f3f4f6;
            color: #9ca3af;
        }

        .enhance-text-btn-square.regenerate-mode {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .enhance-text-btn-square.regenerate-mode:hover {
            background: #5a6268;
            border-color: #545b62;
        }

        /* Checkbox styling for text enhancement */
        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            font-size: 11px;
            line-height: 1.2;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        .checkbox-label input[type="checkbox"]:disabled {
            cursor: not-allowed;
        }

        /* Ensure control groups allow dropdown overflow */
        .control-group {
            position: relative;
            overflow: visible;
            z-index: 1;
        }

        /* Special handling for AI text enhancement control group */
        .control-group:has(.text-enhancement-container) {
            overflow: visible;
            z-index: 100;
        }

        /* All Caps control styling */
        .all-caps-control {
            margin-top: 6px;
        }

        .all-caps-control .checkbox-label {
            font-size: 12px;
        }

        .checkbox-text {
            color: #374151;
        }
    </style>
</head>
<body class="normal">
    <div class="studio-container">
        <main class="main-content">
            <!-- Left Menu -->
            <div class="left-menu">
                <div class="left-menu-item" data-sidebar="menu-sidebar">
                    <img src="/images/ion-menu-outline-icon.svg" alt="Menu">
                </div>
                <div class="left-menu-item" data-sidebar="text-sidebar">
                    <img src="/images/ph-text-t-light-icon.svg" alt="Text">
                </div>
                <div class="left-menu-item" data-sidebar="elements-sidebar">
                    <img src="/images/ph-shapes-light-icon.svg" alt="Elements">
                </div>
                <div class="left-menu-item" data-sidebar="images-sidebar">
                    <img src="/images/ph-images-square-light-icon.svg" alt="Images">
                </div>
                <div class="left-menu-item" data-sidebar="ai-generator-sidebar">
                    <i class="fas fa-magic" style="font-size: 20px; color: #6b7280;"></i>
                </div>
            </div>

            <!-- Left Sidebars -->
            <div class="left-sidebar" id="menu-sidebar">
                <div class="left-sidebar-header">
                    <h3>Menu</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Project Options</div>
                    <ul class="menu-items">
                        <li class="menu-item">New Project</li>
                        <li class="menu-item">My Projects</li>
                        <li class="menu-item" id="saveProjectBtn">Save Project</li>
                        <li class="menu-item" id="updateProjectBtn" style="display: none;">Save Project</li>
                        <li class="menu-item" id="saveAsProjectBtn" style="display: none;">📋 Save As New Project</li>
                        <li class="menu-item">Duplicate Project</li>
                    </ul>
                </div>
            </div>

            <div class="left-sidebar" id="text-sidebar">
                <div class="left-sidebar-header">
                    <h3>Text Library</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Saved Text Styles</div>
                    <div class="text-styles-grid" id="text-styles-grid">
                        <!-- Text styles will be loaded dynamically here -->
                        <div class="loading-message">Loading text styles...</div>
                    </div>
                </div>
            </div>

            <div class="left-sidebar" id="elements-sidebar">
                <div class="left-sidebar-header">
                    <h3>Elements</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <!-- Abstract Shapes Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="abstract">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Abstract</span>
                        </div>
                        <div class="accordion-content" id="abstract-content">
                            <div class="element-grid" id="abstract-grid">
                                <!-- Abstract shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Geometric Shapes Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="geometric">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Geometric</span>
                        </div>
                        <div class="accordion-content" id="geometric-content">
                            <div class="element-grid" id="geometric-grid">
                                <!-- Geometric shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Hand Drawn Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="hand-drawn">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Hand Drawn</span>
                        </div>
                        <div class="accordion-content" id="hand-drawn-content">
                            <div class="element-grid" id="hand-drawn-grid">
                                <!-- Hand drawn shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Ink Brush Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="ink">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Ink</span>
                        </div>
                        <div class="accordion-content" id="ink-content">
                            <div class="element-grid" id="ink-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
                    <!-- Masks Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="masks">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Masks</span>
                        </div>
                        <div class="accordion-content" id="masks-content">
                            <div class="element-grid" id="masks-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Extra Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="extra">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Extra</span>
                        </div>
                        <div class="accordion-content" id="extra-content">
                            <div class="element-grid" id="extra-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
					<!-- Data Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="data">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Data</span>
                        </div>
                        <div class="accordion-content" id="data-content">
                            <div class="element-grid" id="data-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
					<!-- Icons Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="icons">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Icons</span>
                        </div>
                        <div class="accordion-content" id="icons-content">
                            <div class="element-grid" id="icons-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
					<!-- Separators Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="separators">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Separators</span>
                        </div>
                        <div class="accordion-content" id="separators-content">
                            <div class="element-grid" id="separators-grid">
                                <!-- Ink brush shapes will be loaded here -->
                            </div>
                        </div>
                    </div>

                    <!-- Grunge Accordion -->
                    <div class="accordion-item">
                        <div class="accordion-header" data-category="grunge">
                            <span class="accordion-arrow">▶</span>
                            <span class="accordion-title">Grunge</span>
                        </div>
                        <div class="accordion-content" id="grunge-content">
                            <div class="element-grid" id="grunge-grid">
                                <!-- Grunge shapes will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="left-sidebar" id="images-sidebar">
                <div class="left-sidebar-header">
                    <h3>Images</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <div class="section-title">Stock Images</div>
                    <div class="image-grid">
                        <!-- Stock images will be loaded dynamically here -->
                    </div>
                </div>
            </div>

            <!-- AI Generator Sidebar -->
            <div class="left-sidebar" id="ai-generator-sidebar">
                <div class="left-sidebar-header">
                    <h3>AI Generator</h3>
                    <button class="left-sidebar-close">&times;</button>
                </div>
                <div class="left-sidebar-content">
                    <!-- Object Input -->
                    <div class="section-title">What would you like to create?</div>
                    <div class="form-group">
                        <input type="text" id="aiObjectInput" placeholder="Enter what you want to create (e.g., dog, car, flower)" class="ai-input">
                        <div class="ai-object-enhancement-container">
                            <select id="aiToneSelect" class="ai-tone-dropdown">
                                <option value="Generic">Generic</option>
                                <option value="Funny">Funny</option>
                                <option value="Sarcastic">Sarcastic</option>
                                <option value="Cute">Cute</option>
                                <option value="For Kids">For Kids</option>
                                <option value="Horror">Horror</option>
                                <option value="Apocalyptic">Apocalyptic</option>
                                <option value="Romantic - Love">Romantic - Love</option>
                                <option value="Nostalgic">Nostalgic</option>
                                <option value="Cool - Edgy">Cool - Edgy</option>
                                <option value="Dark Humor">Dark Humor</option>
                                <option value="Inspirational">Inspirational</option>
                                <option value="Minimalist">Minimalist</option>
                                <option value="Psychedelic">Psychedelic</option>
                                <option value="Aesthetic - Artsy">Aesthetic - Artsy</option>
                                <option value="Gothic">Gothic</option>
                                <option value="Fantasy">Fantasy</option>
                                <option value="Sci-Fi - Futuristic">Sci-Fi - Futuristic</option>
                                <option value="Patriotic">Patriotic</option>
                                <option value="Sporty - Active">Sporty - Active</option>
                                <option value="Pop Culture">Pop Culture</option>
                            </select>
                            <button type="button" class="ai-enhance-object-btn" onclick="enhanceAIObject(event)"
                                    title="Enhance object description with AI">
                                <i class="fas fa-magic"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Image Style Section -->
                    <div class="section-title">Image Style</div>
                    <div class="form-group">
                        <label>Prompt Templates</label>
                        <div class="ai-template-grid-container">
                            <div class="ai-template-grid" id="aiTemplateGrid">
                                <!-- Templates will be loaded here -->
                            </div>
                            <div class="ai-custom-scrollbar" id="aiCustomScrollbar">
                                <div class="ai-custom-scrollbar-thumb" id="aiCustomScrollbarThumb"></div>
                            </div>
                        </div>
                    </div>
                    <!-- Color Texts Section -->
                    <div class="section-title">Texts</div>
                    <div class="form-group" id="textToneContainer">
                        <label>Text Tone</label>
                        <select id="aiTextToneSelect" class="ai-tone-dropdown">
                            <option value="Generic">Generic</option>
                            <option value="Funny">Funny</option>
                            <option value="Sarcastic">Sarcastic</option>
                            <option value="Cute">Cute</option>
                            <option value="For Kids">For Kids</option>
                            <option value="Horror">Horror</option>
                            <option value="Apocalyptic">Apocalyptic</option>
                            <option value="Romantic - Love">Romantic - Love</option>
                            <option value="Nostalgic">Nostalgic</option>
                            <option value="Cool - Edgy">Cool - Edgy</option>
                            <option value="Dark Humor">Dark Humor</option>
                            <option value="Inspirational - Motivational">Inspirational - Motivational</option>
                            <option value="Minimalist">Minimalist</option>
                            <option value="Psychedelic">Psychedelic</option>
                            <option value="Aesthetic - Artsy">Aesthetic - Artsy</option>
                            <option value="Gothic">Gothic</option>
                            <option value="Fantasy">Fantasy</option>
                            <option value="Sci-Fi - Futuristic">Sci-Fi - Futuristic</option>
                            <option value="Patriotic">Patriotic</option>
                            <option value="Sporty - Active">Sporty - Active</option>
                            <option value="Pop Culture - Meme">Pop Culture - Meme</option>
                        </select>
                    </div>
                    <div class="ai-checkbox-row-side">
                        <label class="checkbox-label">
                            <input type="checkbox" id="replaceAllTextsCheckboxform">
                            <span class="checkbox-text">Replace All Texts</span>
                        </label>
                    </div>

                    <!-- Color Palette Section -->
                    <div class="section-title">Color Palette</div>
                    <div class="form-group">
                        <color-palette-selector id="aiColorPaletteSelector"></color-palette-selector>
                    </div>

                    <!-- Include Texts and Shapes Checkbox -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeTextsCheckbox">
                            <span class="checkbox-text">Include Texts and Shapes Colors</span>
                        </label>
                        <small class="helper-text" style="color: #666; font-size: 11px; display: block; margin-top: 3px;">
                            Only texts and shapes with assigned color intensity will be updated
                        </small>
                    </div>

                    <!-- Generate Button -->
                    <div class="form-group">
                        <button id="aiGenerateBtn" class="ai-generate-btn">
                            <i class="fas fa-magic"></i> Generate Image
                        </button>
                    </div>

                    <!-- Loading State -->
                    <div id="aiLoadingContainer" class="ai-loading-container" style="display: none;">
                        <div class="ai-loading-spinner"></div>
                        <p>Generating your image...</p>
                    </div>
                </div>
            </div>
            <div class="canvas-area" id="canvas-area">
                <canvas id="demo" width="2048" height="2048"></canvas>
                 <div class="canvas-controls">
                      <button id="zoomOutBtn" title="Zoom Out">-</button>
                      <span class="zoom-level" id="zoomLevel">100%</span>
                      <button id="zoomInBtn" title="Zoom In">+</button>
                      <div class="separator"></div> <!-- Separator -->
                      <!-- Div for Pickr initialization -->
                      <div id="canvasBgColorPicker" title="Change Background Color"></div>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="toggleArtboardBtn" title="Toggle Artboard">Artboard</button>
                      <button id="addToCollectionBtn" title="Add Artboard to Collection">Add to Collection</button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="performanceToggleBtn" title="Toggle Performance Mode (reduces quality during movement for better performance)">⚡ Performance</button>
                      <span id="performanceIndicator" class="performance-indicator" title="Click to view performance report">🟢</span>
                      <div class="separator"></div> <!-- Separator -->

                      <!-- ALIGNMENT CONTROLS (shown only when multiple objects are selected) -->
                      <div class="alignment-controls" id="alignment-controls" style="display: none;">
                          <span class="alignment-label" id="alignment-label">Align Objects:</span>
                          <button id="alignLeftBtn" class="align-btn" title="Align Left">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                  <line x1="3" y1="6" x2="21" y2="6"></line>
                                  <line x1="3" y1="12" x2="15" y2="12"></line>
                                  <line x1="3" y1="18" x2="18" y2="18"></line>
                              </svg>
                          </button>
                          <button id="alignCenterBtn" class="align-btn" title="Align Center">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                  <line x1="18" y1="6" x2="6" y2="6"></line>
                                  <line x1="21" y1="12" x2="3" y2="12"></line>
                                  <line x1="16" y1="18" x2="8" y2="18"></line>
                              </svg>
                          </button>
                          <button id="alignRightBtn" class="align-btn" title="Align Right">
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                  <line x1="21" y1="6" x2="3" y2="6"></line>
                                  <line x1="21" y1="12" x2="9" y2="12"></line>
                                  <line x1="21" y1="18" x2="6" y2="18"></line>
                              </svg>
                          </button>
                          <span class="selected-count" id="selected-count">2 objects selected</span>
                      </div>
                      <div class="separator" id="alignment-separator" style="display: none;"></div> <!-- Separator for alignment -->
                      <button id="moveForwardBtn" title="Move Forward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Forward" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <button id="moveBackwardBtn" title="Move Backward" disabled>
                          <img src="/images/radix-icons-stack-icon.svg" alt="Backward" style="width: 16px; height: 16px; vertical-align: middle; transform: rotate(180deg);">
                      </button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="copyElementBtn" title="Copy Selected Element" disabled>
                          <img src="/images/icons/ph-copy-light-icon.svg" alt="Copy" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <button id="pasteElementBtn" title="Paste Element" disabled>
                          <img src="/images/icons/ph-clipboard-light-icon.svg" alt="Paste" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                      <div class="separator"></div> <!-- Separator -->
                      <button id="undoBtn" title="Undo" disabled>
                          <img src="/images/icons/redo.svg" alt="Undo" style="width: 16px; height: 16px; vertical-align: middle; transform: scaleX(-1);">
                      </button>
                      <button id="redoBtn" title="Redo" disabled>
                          <img src="/images/icons/redo.svg" alt="Redo" style="width: 16px; height: 16px; vertical-align: middle;">
                      </button>
                 </div>
            </div>

            <aside class="sidebar">
                <div class="sidebar-tabs">
                    <button class="sidebar-tab active" data-tab="text-tab-content">Text</button>
                    <button class="sidebar-tab" data-tab="image-tab-content">Image</button>
                    <button class="sidebar-tab" data-tab="admin-tab-content">Admin</button> <!-- Added Admin Tab -->
                </div>

                <!-- TEXT TAB CONTENT -->
                <div class="sidebar-content active" id="text-tab-content">
                    <div class="action-buttons">
                        <button id="addEditTextBtn" class="add-btn">Add</button>
                        <button id="deleteTextBtn" class="delete-btn" title="Delete Selected Text" disabled>Delete</button>
                    </div>
                    <div class="text-properties-header">Text Properties</div>
                    <div class="text-property-tabs">
                        <button class="property-tab active" data-panel="basic-panel">Basic</button>
                        <button class="property-tab" data-panel="distort-panel">Distort</button>
                        <button class="property-tab" data-panel="shadow-panel">Shadow</button>
                        <button class="property-tab" data-panel="decor-panel">Decor</button>
                    </div>
                     <!-- TEXT Panels Wrapper -->
                     <div id="text-controls">
                        <!-- BASIC -->
                        <div class="property-panel active basic-panel">
                            <div class="control-group">
                                <label for="iText">Text:</label>
                                <input id="iText" type="text" value="" placeholder="Type text here...">
                            </div>

                            <!-- AI Text Enhancement Section -->
                            <div class="ai-enhancement-section">
                                <div class="ai-enhancement-layout">
                                    <select id="textToneSelect" class="text-tone-dropdown-full" disabled>
                                        <option value="Generic">Generic</option>
                                        <option value="Funny">Funny</option>
                                        <option value="Sarcastic">Sarcastic</option>
                                        <option value="Cute">Cute</option>
                                        <option value="For Kids">For Kids</option>
                                        <option value="Horror">Horror</option>
                                        <option value="Apocalyptic">Apocalyptic</option>
                                        <option value="Romantic - Love">Romantic - Love</option>
                                        <option value="Nostalgic">Nostalgic</option>
                                        <option value="Cool - Edgy">Cool - Edgy</option>
                                        <option value="Dark Humor">Dark Humor</option>
                                        <option value="Inspirational">Inspirational</option>
                                        <option value="Minimalist">Minimalist</option>
                                        <option value="Psychedelic">Psychedelic</option>
                                        <option value="Aesthetic - Artsy">Aesthetic - Artsy</option>
                                        <option value="Gothic">Gothic</option>
                                        <option value="Fantasy">Fantasy</option>
                                        <option value="Sci-Fi - Futuristic">Sci-Fi - Futuristic</option>
                                        <option value="Patriotic">Patriotic</option>
                                        <option value="Sporty - Active">Sporty - Active</option>
                                        <option value="Pop Culture">Pop Culture</option>
                                    </select>
                                    <button type="button" id="enhanceTextBtn" class="enhance-text-btn-square" onclick="enhanceSelectedText()" disabled
                                            title="Enhance selected text with AI">
                                        <i class="fas fa-magic"></i>
                                    </button>
                                </div>
                                <div class="ai-checkbox-row">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="replaceAllTextsCheckbox" disabled>
                                        <span class="checkbox-text">Replace All Texts</span>
                                    </label>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iTextColor">Text Color:</label>
                                <div class="simplified-color-picker">
                                    <input id="iTextColor" type="color" value="#FF0000">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iTextColorIntensity">Color Intensity:</label>
                                <select id="iTextColorIntensity" style="width: 100%; padding: 6px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #fff;">
                                    <option value="no-change">No Change (keep current color)</option>
                                    <option value="lightest">Lightest (Lightest color from palette)</option>
                                    <option value="light">Light-Medium (Light-medium colors from palette)</option>
                                    <option value="medium">Medium (Medium colors from palette)</option>
                                    <option value="dark">Dark-Medium (Dark-medium colors from palette)</option>
                                    <option value="darkest">Darkest (Darkest colors from palette)</option>
                                </select>
                                <small style="color: #666; font-size: 11px; display: block; margin-top: 3px;">
                                    Choose how this text should use colors from the selected palette
                                </small>
                                <!-- Debug display for persistent Color Intensity -->
                                <div style="margin-top: 8px; padding: 8px; background-color: #f0f9ff; border: 2px solid #3b82f6; border-radius: 6px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);">
                                    <span style="font-size: 12px; color: #1e40af; font-weight: bold;">💾 Debug newColorIntensity: </span>
                                    <span id="debugNewColorIntensity" style="font-size: 12px; color: #1e40af; font-weight: bold;">N/A</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iFontFamily">Font:</label>
                                <select id="iFontFamily" disabled>
                                    <option value="Poppins">Poppins</option>
                                    <option value="Arial">Arial</option>
                                    <option value="Verdana">Verdana</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                    <option value="Courier New">Courier New</option>
                                    <option value="Impact">Impact</option>
                                    <option value="Comic Sans MS">Comic Sans MS</option>
                                </select>
                            </div>
                            <div class="control-group">
                                <label>Style:</label>
                                <div class="font-style-controls">
                                    <label for="iBold">B</label><input id="iBold" type="checkbox" disabled>
                                    <label for="iItalic">I</label><input id="iItalic" type="checkbox" disabled>
                                </div>
                                <div class="all-caps-control">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="iAllCaps" disabled>
                                        <span class="checkbox-text">All Caps</span>
                                    </label>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iFontSize">Font Size:</label>
                                <div class="slider-container">
                                    <input id="iFontSize" type="range" min="10" max="500" value="100" step="1" disabled>
                                    <span class="slider-value" id="vFontSize">100px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iTextRotation">Rotation:</label>
                                <div class="slider-container">
                                    <input id="iTextRotation" type="range" min="-180" max="180" value="0" step="1">
                                    <span class="slider-value" id="vTextRotation">0°</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iLetterSpacing">Letter Spacing:</label>
                                <div class="slider-container">
                                    <input id="iLetterSpacing" type="range" min="-20" max="100" value="0" step="1" disabled>
                                    <span class="slider-value" id="vLetterSpacing">0px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input id="iOpacity" type="range" min="1" max="100" value="100" step="1" disabled>
                                    <span class="slider-value" id="vOpacity">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="strokeToggle">Stroke:</label>
                                <select id="strokeToggle" disabled>
                                    <option value="noStroke" selected>No Stroke</option>
                                    <option value="stroke">Standard</option>
                                </select>
                            </div>
                            <div class="parameter-control stroke-param">
                                <h4>Standard Stroke</h4>
                                <div class="control-group">
                                    <label for="strokeWidth">Width:</label>
                                    <div class="slider-container">
                                        <input type="range" id="strokeWidth" min="0" max="5" value="0.5" step="0.1" disabled>
                                        <span class="slider-value" id="vStrokeWidth">0.5px</span>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label for="strokeColor">Color:</label>
                                    <div class="simplified-color-picker">
                                        <input type="color" id="strokeColor" value="#000000" disabled>
                                    </div>
                                </div>
                                <div class="control-group">
                                    <label for="strokeOpacity">Opacity:</label>
                                    <div class="slider-container">
                                        <input type="range" id="strokeOpacity" min="0" max="100" value="100" step="1" disabled>
                                        <span class="slider-value" id="vStrokeOpacity">100%</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Font Styles Management -->
                            <div class="control-group" style="border-top: 2px solid #10b981; margin-top: 15px; padding-top: 15px;">
                                <button id="changeFontsBtn" style="width: 100%; padding: 8px; background-color: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em; margin-bottom: 8px;">
                                    🔄 Change Fonts
                                </button>
                                <small style="color: #666; font-size: 11px; display: block; margin-bottom: 8px;">
                                    Cycle through saved font style configurations
                                </small>
                                <button id="saveFontStyleBtn" style="width: 100%; padding: 8px; background-color: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em; margin-bottom: 8px;">
                                    💾 Save Font Style (Admin)
                                </button>
                                <small style="color: #666; font-size: 11px; display: block; margin-bottom: 8px;">
                                    Save current font configuration of all texts for cycling
                                </small>
                                <div id="fontStylesStatus" style="font-size: 11px; color: #666; padding: 4px; background-color: #f8f9fa; border-radius: 4px; margin-bottom: 8px;">
                                    No saved font styles
                                </div>
                            </div>

                            <!-- Decor Styles Management -->
                            <div class="control-group" style="border-top: 2px solid #8b5cf6; margin-top: 15px; padding-top: 15px;">
                                <button id="changeDecorBtn" style="width: 100%; padding: 8px; background-color: #8b5cf6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em; margin-bottom: 8px;">
                                    🎨 Change Decor Styles
                                </button>
                                <small style="color: #666; font-size: 11px; display: block; margin-bottom: 8px;">
                                    Cycle through saved decoration and shadow configurations
                                </small>
                                <button id="saveDecorStyleBtn" style="width: 100%; padding: 8px; background-color: #7c3aed; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em; margin-bottom: 8px;">
                                    💾 Save Decor Style (Admin)
                                </button>
                                <small style="color: #666; font-size: 11px; display: block; margin-bottom: 8px;">
                                    Save current decoration/shadow configuration of all objects for cycling
                                </small>
                                <div id="decorStylesStatus" style="font-size: 11px; color: #666; padding: 4px; background-color: #f8f9fa; border-radius: 4px; margin-bottom: 8px;">
                                    No saved decor styles
                                </div>
                            </div>

                            <!-- Color Palette Management -->
                            <div class="control-group" style="border-top: 2px solid #f59e0b; margin-top: 15px; padding-top: 15px;">
                                <button id="changeColorsBtn" style="width: 100%; padding: 8px; background-color: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.9em; margin-bottom: 8px;">
                                    🌈 Change Colors
                                </button>
                                <small style="color: #666; font-size: 11px; display: block; margin-bottom: 8px;">
                                    Randomly select a new palette and apply colors based on assigned intensities
                                </small>
                                <div id="colorChangeStatus" style="font-size: 11px; color: #666; padding: 4px; background-color: #f8f9fa; border-radius: 4px; margin-bottom: 8px;">
                                    Ready to apply random palette colors
                                </div>
                            </div>

                            <!-- Template ID for text replacement -->
                            <div class="control-group" style="border-top: 2px solid #e74c3c; margin-top: 15px; padding-top: 15px;">
                                <label for="textTemplateId" style="color: #e74c3c; font-weight: bold;">Template ID:</label>
                                <select id="textTemplateId" style="border: 2px solid #e74c3c; background-color: #fff5f5;" disabled>
                                    <option value="">Not Replaceable</option>
                                    <option value="t01">t01 - Text 1</option>
                                    <option value="t02">t02 - Text 2</option>
                                    <option value="t03">t03 - Text 3</option>
                                    <option value="t04">t04 - Text 4</option>
                                    <option value="t05">t05 - Text 5</option>
                                    <option value="t06">t06 - Text 6</option>
                                    <option value="t07">t07 - Text 7</option>
                                    <option value="t08">t08 - Text 8</option>
                                    <option value="t09">t09 - Text 9</option>
                                    <option value="t10">t10 - Text 10</option>
                                </select>
                                <small style="color: #7f8c8d; font-size: 11px; display: block; margin-top: 5px;">
                                    Select an ID to make this text replaceable in templates. Leave as "Not Replaceable" to keep text fixed.
                                </small>
                            </div>
                            <!-- Debug display for persistent Template ID -->
                                <div style="margin-top: 8px; padding: 8px; background-color: #fef2f2; border: 2px solid #e74c3c; border-radius: 6px; box-shadow: 0 2px 4px rgba(231, 76, 60, 0.1);">
                                    <span style="font-size: 12px; color: #dc2626; font-weight: bold;">🔗 Debug newTemplateId: </span>
                                    <span id="debugNewTemplateId" style="font-size: 12px; color: #dc2626; font-weight: bold;">N/A</span>
                                </div>
                        </div>

                        <!-- DISTORT -->
                        <div class="property-panel distort-panel"> <div class="control-group"> <label for="effectMode">Effect:</label> <select id="effectMode" disabled> <option value="normal">Normal</option> <option value="skew">Skew</option> <option value="warp">Warp</option> <option value="curve">Curved</option> <option value="circle">Circular</option> <option value="mesh">Mesh Warp</option> <option value="grid-distort">Grid Distort</option> </select> </div> <div class="parameter-control horizontal-skew control-group" id="horizontalSkewControl"> <label>Skew X:</label> <div class="slider-container"><input type="range" id="skewSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkew">0</span></div> </div> <div class="parameter-control vertical-skew control-group" id="verticalSkewControl"> <label>Skew Y:</label> <div class="slider-container"><input type="range" id="skewYSlider" min="-50" max="50" value="0" step="1" disabled><span class="slider-value" id="vSkewY">0</span></div> </div> <div class="parameter-control mesh-param"> <h4>Mesh Settings</h4> <div class="control-group"><label>Columns:</label><div class="slider-container"><input id="iMeshCols" type="range" min="3" max="9" value="5" step="1" disabled><span class="slider-value" id="vMeshCols">5</span></div></div> <div class="control-group"><label>Rows:</label><div class="slider-container"><input id="iMeshRows" type="range" min="2" max="8" value="4" step="1" disabled><span class="slider-value" id="vMeshRows">4</span></div></div> <div class="control-group"><button id="resetMeshBtn" disabled>Reset Points</button></div> <div class="control-group"><button id="toggleMeshBtn" disabled>Show Grid</button></div> </div> <div class="parameter-control warp-param"> <h4>Warp Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurve" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vCurve">100</span></div></div> <div class="control-group"><label>Offset Y:</label><div class="slider-container"><input id="iOffset" type="range" min=-500 max=500 value=10 disabled><span class="slider-value" id="vOffset">10</span></div></div> <div class="control-group"><label>Height:</label><div class="slider-container"><input id="iHeight" type="range" min=-500 max=500 value=100 disabled><span class="slider-value" id="vHeight">100</span></div></div> <div class="control-group"><label>Bottom:</label><div class="slider-container"><input id="iBottom" type="range" min=-500 max=500 value=150 disabled><span class="slider-value" id="vBottom">150</span></div></div> <div class="control-group"><label>Triangle:</label> <input id="iTriangle" type="checkbox" disabled></div> <div class="control-group shift-center-control"><label>Shift Center:</label><div class="slider-container"><input id="iShiftCenter" type="range" min="0" max="200" value="100" disabled><span class="slider-value" id="vShiftCenter">100</span></div></div> </div> <div class="parameter-control circle-param"> <h4>Circular Settings</h4> <div class="control-group"><label>Diameter:</label><div class="slider-container"><input id="iDiameter" type="range" min=100 max=1500 value=600 step=1 disabled><span class="slider-value" id="vDiameter">600px</span></div></div> <div class="control-group"><label>Kerning:</label><div class="slider-container"><input id="iKerning" type="range" min=-20 max=50 value="0" step=1 disabled><span class="slider-value" id="vKerning">0px</span></div></div> <div class="control-group"><label>Rotation:</label><div class="slider-container"><input id="iCircleRotation" type="range" min="-180" max="180" value="0" step="1" disabled><span class="slider-value" id="vCircleRotation">0°</span></div></div> <div class="control-group"><label>Flip:</label><input id="iFlip" type="checkbox" disabled></div> </div> <div class="parameter-control curve-param"> <h4>Curved Settings</h4> <div class="control-group"><label>Curve:</label><div class="slider-container"><input id="iCurveAmount" type="range" min=-100 max=100 value=40 step=1 disabled><span class="slider-value" id="vCurveAmount">40</span></div></div> <div class="control-group"><label>Spacing:</label><div class="slider-container"><input id="iCurveKerning" type="range" min=-20 max=50 value=0 step=1 disabled><span class="slider-value" id="vCurveKerning">0px</span></div></div> <div class="control-group"><label>Flip:</label><input id="iCurveFlip" type="checkbox" disabled></div> </div>
<div class="parameter-control grid-distort-param"> <h4>Grid Distort Settings</h4> <div class="control-group"><label>Columns:</label><div class="slider-container"><input id="iGridDistortCols" type="range" min="1" max="5" value="2" step="1" disabled><span class="slider-value" id="vGridDistortCols">2</span></div></div> <div class="control-group"><label>Rows:</label><div class="slider-container"><input id="iGridDistortRows" type="range" min="1" max="5" value="1" step="1" disabled><span class="slider-value" id="vGridDistortRows">1</span></div></div> <div class="control-group"><label>Padding:</label><div class="slider-container"><input id="iGridDistortPadding" type="range" min="20" max="200" value="80" step="1" disabled><span class="slider-value" id="vGridDistortPadding">80px</span></div></div> <div class="control-group"><label>Intensity:</label><div class="slider-container"><input id="iGridDistortIntensity" type="range" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vGridDistortIntensity">100%</span></div></div> <div class="control-group"><label>Direction:</label><div class="direction-options"><label><input type="radio" name="gridDistortDirection" id="gridDistortDirectionBoth" value="both" disabled> Both</label><label><input type="radio" name="gridDistortDirection" id="gridDistortDirectionVertical" value="vertical" checked disabled> Vertical Only</label></div></div> <div class="control-group"><button id="resetGridDistortBtn" disabled>Reset Grid</button></div> <div class="control-group"><button id="toggleGridDistortBtn" disabled>Show Grid</button></div> </div> </div>
                        <!-- SHADOW --> <div class="property-panel shadow-panel"> <div class="control-group"> <label for="shadow">Shadow:</label> <select id="shadow" disabled> <option value="noShadow" selected>No Shadow</option> <option value="shadow">Standard</option> <option value="blockShadow">Block</option> <option value="perspectiveShadow">Perspective Shadow</option> <option value="lineShadow">Line</option> <option value="detailed3D">Detailed 3D</option> </select> </div> <div class="parameter-control shadow-param"> <h4>Standard Shadow</h4> <div class="control-group"><label for="shadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="shadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="shadowOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="shadowOffsetX" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetX">5px</span></div></div> <div class="control-group"><label for="shadowOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="shadowOffsetY" class="slider" min="-50" max="50" value="5" step="1" disabled><span class="slider-value" id="vShadowOffsetY">5px</span></div></div> <div class="control-group"><label for="shadowBlur">Blur:</label><div class="slider-container"><input type="range" id="shadowBlur" class="slider" min="0" max="50" value="10" step="1" disabled><span class="slider-value" id="vShadowBlur">10px</span></div></div> </div> <div class="parameter-control block-shadow-param"> <h4>Block Shadow</h4> <div class="control-group"><label for="blockShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="blockShadowColor" value="#000000" disabled></div></div> <div class="control-group"><label for="blockShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="blockShadowOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vBlockShadowOpacity">100%</span></div></div> <div class="control-group"><label for="blockShadowOffset">Distance:</label><div class="slider-container"><input type="range" id="blockShadowOffset" class="slider" min="0" max="200" value="40" step="1" disabled><span class="slider-value" id="vBlockShadowOffset">40px</span></div></div> <div class="control-group"><label for="blockShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="blockShadowAngle" class="slider" min="-180" max="180" value="-58" step="1" disabled><span class="slider-value" id="vBlockShadowAngle">-58°</span></div></div> <div class="control-group"><label for="blockShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="blockShadowBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vBlockShadowBlur">5px</span></div></div> <div class="control-group"><label for="blockShadowPerspective">Perspective:</label><div class="switch-container"><label class="switch-label"><input type="checkbox" id="blockShadowPerspective" disabled><span class="switch-custom"></span>Enable Perspective</label></div></div>
<div class="control-group perspective-control" style="display: none; margin-top: 10px; padding: 8px; background-color: #f0f9ff; border-left: 3px solid #3b82f6; border-radius: 4px;"><label for="blockShadowPerspectiveIntensity">Perspective Intensity:</label><div class="slider-container"><input type="range" id="blockShadowPerspectiveIntensity" class="slider" min="1" max="100" value="50" step="1" disabled><span class="slider-value" id="vBlockShadowPerspectiveIntensity">50%</span></div><div class="control-hint">Higher values make distant shadows smaller, 1% = almost no perspective</div></div> </div>

<div class="parameter-control perspective-shadow-param"> <h4>Perspective Shadow</h4> <div class="control-group"><label for="perspectiveShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="perspectiveShadowColor" value="#333333" disabled></div></div> <div class="control-group"><label for="perspectiveShadowOpacity">Opacity:</label><div class="slider-container"><input type="range" id="perspectiveShadowOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOpacity">100%</span></div></div> <div class="control-group"><label for="perspectiveShadowOffset">Distance:</label><div class="slider-container"><input type="range" id="perspectiveShadowOffset" class="slider" min="0" max="20" value="6" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOffset">6px</span></div></div> <div class="control-group"><label for="perspectiveShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="perspectiveShadowAngle" class="slider" min="-180" max="180" value="105" step="1" disabled><span class="slider-value" id="vPerspectiveShadowAngle">105°</span></div></div> <div class="control-group"><label for="perspectiveShadowBlur">Blur:</label><div class="slider-container"><input type="range" id="perspectiveShadowBlur" class="slider" min="0" max="50" value="2" step="1" disabled><span class="slider-value" id="vPerspectiveShadowBlur">2px</span></div></div> <div class="control-group"><label for="perspectiveShadowIntensity">Perspective Intensity:</label><div class="slider-container"><input type="range" id="perspectiveShadowIntensity" class="slider" min="1" max="100" value="16" step="1" disabled><span class="slider-value" id="vPerspectiveShadowIntensity">16%</span></div><div class="control-hint">Higher values make distant shadows smaller, 1% = almost no perspective</div></div>
<h5>Front Outline</h5>
<div class="control-group"><label for="perspectiveShadowOutlineColor">Color:</label><div class="simplified-color-picker"><input type="color" id="perspectiveShadowOutlineColor" value="#d1d5db" disabled></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOpacity">Opacity:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOpacity">100%</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineWidth">Width:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineWidth" class="slider" min="0" max="30" value="3" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineWidth">3px</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOffsetX" class="slider" min="-50" max="50" value="2" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOffsetX">2px</span></div></div>
<div class="control-group"><label for="perspectiveShadowOutlineOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="perspectiveShadowOutlineOffsetY" class="slider" min="-50" max="50" value="-3" step="1" disabled><span class="slider-value" id="vPerspectiveShadowOutlineOffsetY">-3px</span></div></div>
</div> <div class="parameter-control line-shadow-param"> <h4>Line Shadow</h4> <div class="control-group"><label for="lineShadowColor">Color:</label><div class="simplified-color-picker"><input type="color" id="lineShadowColor" value="#AAAAAA" disabled></div></div> <div class="control-group"><label for="lineShadowDistance">Distance:</label><div class="slider-container"><input type="range" id="lineShadowDistance" class="slider" min="0" max="100" value="15" step="1" disabled><span class="slider-value" id="vLineShadowDistance">15px</span></div></div> <div class="control-group"><label for="lineShadowAngle">Angle:</label><div class="slider-container"><input type="range" id="lineShadowAngle" class="slider" min="-180" max="180" value="-45" step="1" disabled><span class="slider-value" id="vLineShadowAngle">-45°</span></div></div> <div class="control-group"><label for="lineShadowThickness">Thickness:</label><div class="slider-container"><input type="range" id="lineShadowThickness" class="slider" min="1" max="30" value="5" step="1" disabled><span class="slider-value" id="vLineShadowThickness">5px</span></div></div> </div> <div class="parameter-control detailed-3d-param"> <h4>Detailed 3D</h4> <h5>Extrusion</h5> <div class="control-group"><label for="detailed3DPrimaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DPrimaryColor" value="#000000" disabled></div></div> <div class="control-group"><label for="detailed3DPrimaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DPrimaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DPrimaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DOffset">Distance:</label><div class="slider-container"><input type="range" id="detailed3DOffset" class="slider" min="0" max="200" value="36" step="1" disabled><span class="slider-value" id="vDetailed3DOffset">36px</span></div></div> <div class="control-group"><label for="detailed3DAngle">Angle:</label><div class="slider-container"><input type="range" id="detailed3DAngle" class="slider" min="-180" max="180" value="-63" step="1" disabled><span class="slider-value" id="vDetailed3DAngle">-63°</span></div></div> <div class="control-group"><label for="detailed3DBlur">Blur:</label><div class="slider-container"><input type="range" id="detailed3DBlur" class="slider" min="0" max="50" value="5" step="1" disabled><span class="slider-value" id="vDetailed3DBlur">5px</span></div></div> <h5>Front Outline</h5> <div class="control-group"><label for="detailed3DSecondaryColor">Color:</label><div class="simplified-color-picker"><input type="color" id="detailed3DSecondaryColor" value="#00FF00" disabled></div></div> <div class="control-group"><label for="detailed3DSecondaryOpacity">Opacity:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOpacity">100%</span></div></div> <div class="control-group"><label for="detailed3DSecondaryWidth">Width:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryWidth" class="slider" min="0" max="30" value="4" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryWidth">4px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetX">Offset X:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetX" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetX">-5px</span></div></div> <div class="control-group"><label for="detailed3DSecondaryOffsetY">Offset Y:</label><div class="slider-container"><input type="range" id="detailed3DSecondaryOffsetY" class="slider" min="-50" max="50" value="-5" step="1" disabled><span class="slider-value" id="vDetailed3DSecondaryOffsetY">-5px</span></div></div> </div> </div>
                        <!-- DECOR --> <div class="property-panel decor-panel"> <div class="control-group"> <label for="linesDecoration">Fill Decor:</label> <select id="linesDecoration" disabled> <option value="noDecoration">None</option> <option value="horizontalLines">Horizontal Lines</option> <option value="colorCut">Color Cut</option> <option value="obliqueLines">Oblique Lines</option> <option value="fadingLinesCut">Fading Lines</option> <option value="diagonalLines">Diagonal Lines</option> </select> </div> <div class="parameter-control horizontal-lines-param"> <h4>Horizontal Lines</h4> <div class="control-group"><label for="hWeight">Weight:</label><div class="slider-container"><input type="range" id="hWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vHWeight">3px</span></div></div> <div class="control-group"><label for="hDistance">Distance:</label><div class="slider-container"><input type="range" id="hDistance" min="1" max="50" value="7" disabled><span class="slider-value" id="vHDistance">7px</span></div></div> <div class="control-group"><label for="hColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="hColor" value="#0000FF" disabled></div></div> <div class="control-group"><label for="hOpacity">Opacity:</label><div class="slider-container"><input type="range" id="hOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vHOpacity">100%</span></div></div> </div> <div class="parameter-control color-cut-param"> <h4>Color Cut</h4> <div class="control-group"><label for="ccDistance">Cut (%):</label><div class="slider-container"><input type="range" id="ccDistance" min="1" max="100" value="50" disabled><span class="slider-value" id="vCcDistance">50%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillTop" value="top" checked disabled><span>Top</span></label><label class="radio-container"><input type="radio" name="ccFillDirection" id="ccFillBottom" value="bottom" disabled><span>Bottom</span></label></div></div> <div class="control-group"><label for="ccColor">Cut Color:</label><div class="simplified-color-picker"><input type="color" id="ccColor" value="#00FF00" disabled></div></div> <div class="control-group"><label for="ccOpacity">Opacity:</label><div class="slider-container"><input type="range" id="ccOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vCcOpacity">100%</span></div></div> </div> <div class="parameter-control oblique-lines-param"> <h4>Oblique Lines</h4> <div class="control-group"><label for="oWeight">Weight:</label><div class="slider-container"><input type="range" id="oWeight" min="1" max="30" value="4" disabled><span class="slider-value" id="vOWeight">4px</span></div></div> <div class="control-group"><label for="oDistance">Distance:</label><div class="slider-container"><input type="range" id="oDistance" min="1" max="50" value="3" disabled><span class="slider-value" id="vODistance">3px</span></div></div> <div class="control-group"><label for="oColor">Line Color:</label><div class="simplified-color-picker"><input type="color" id="oColor" value="#0000FF" disabled></div></div> <div class="control-group"><label for="oOpacity">Opacity:</label><div class="slider-container"><input type="range" id="oOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vOOpacity">100%</span></div></div> </div> <div class="parameter-control fading-lines-cut-param"> <h4>Fading Lines</h4> <div class="control-group"><label for="flcDistance">Cut (%):</label><div class="slider-container"><input type="range" id="flcDistance" min="1" max="100" value="62" disabled><span class="slider-value" id="vFlcDistance">62%</span></div></div> <div class="control-group fill-direction"><label>Direction:</label><div class="radio-group"><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillTop" value="top" checked disabled><span>Solid Top</span></label><label class="radio-container"><input type="radio" name="flcFillDirection" id="flcFillBottom" value="bottom" disabled><span>Solid Bottom</span></label></div></div> <div class="control-group"><label for="flcColor">Line/Fill:</label><div class="simplified-color-picker"><input type="color" id="flcColor" value="#cccccc" disabled></div></div> <div class="control-group"><label for="flcOpacity">Opacity:</label><div class="slider-container"><input type="range" id="flcOpacity" class="slider" min="0" max="100" value="100" step="1" disabled><span class="slider-value" id="vFlcOpacity">100%</span></div></div> <div class="control-group"><label for="flcMaxWeight">Weight:</label><div class="slider-container"><input type="range" id="flcMaxWeight" min="1" max="30" value="3" disabled><span class="slider-value" id="vFlcMaxWeight">3px</span></div></div> <div class="control-group"><label for="flcSpacing">Spacing:</label><div class="slider-container"><input type="range" id="flcSpacing" min="1" max="40" value="10" disabled><span class="slider-value" id="vFlcSpacing">10px</span></div></div> </div> </div>
                     </div> <!-- End Text Controls Wrapper -->
                </div><!-- End Text Tab -->

                <!-- IMAGE TAB CONTENT -->
                <div class="sidebar-content" id="image-tab-content">
                     <div class="action-buttons">
                        <input type="file" id="image-file-input" accept="image/*">
                        <button id="addImageBtn" class="add-btn">Add Image</button>
                        <button id="deleteImageBtn" class="delete-btn" title="Delete Selected Image" disabled>Delete</button>
                    </div>
                    <div class="image-properties-header">Image Properties</div>
                    <div id="image-controls">
                        <div class="control-group">
                            <label for="iImageSize">Size:</label>
                            <div class="slider-container"> <input id="iImageSize" type="range" min="0.1" max="5" value="1" step="0.05"> <span class="slider-value" id="vImageSize">100%</span> </div>
                        </div>
                        <div class="control-group">
                            <label for="iImageRotation">Rotation:</label>
                            <div class="slider-container"> <input id="iImageRotation" type="range" min="-180" max="180" value="0" step="1"> <span class="slider-value" id="vImageRotation">0°</span> </div>
                        </div>
                        <div class="control-group">
                            <label for="iImageOpacity">Opacity:</label>
                            <div class="slider-container">
                                <input type="range" id="iImageOpacity" min="0" max="100" value="100" step="1">
                                <span class="slider-value" id="vImageOpacity">100%</span>
                            </div>
                        </div>
                        <div class="control-group" id="imageColorGroup" style="display: none;">
                            <label for="iImageColor">Color:</label>
                            <div class="simplified-color-picker"><input type="color" id="iImageColor" value="#000000"></div>
                        </div>
                        <div class="control-group" id="imageColorIntensityGroup" style="display: none;">
                            <label for="iImageColorIntensity">Color Intensity:</label>
                            <select id="iImageColorIntensity" style="width: 100%; padding: 6px; border: 1px solid #d1d5db; border-radius: 4px; background-color: #fff;">
                                <option value="no-change">No Change (keep current color)</option>
                                <option value="lightest">Lightest (Lightest color from palette)</option>
                                <option value="light">Light-Medium (Light-medium colors from palette)</option>
                                <option value="medium">Medium (Medium colors from palette)</option>
                                <option value="dark">Dark-Medium (Dark-medium colors from palette)</option>
                                <option value="darkest">Darkest (Darkest colors from palette)</option>
                            </select>
                            <small style="color: #666; font-size: 11px; display: block; margin-top: 3px;">
                                Choose how this shape should use colors from the selected palette
                            </small>
                            <!-- Debug display for persistent Color Intensity (Images) -->
                            <div style="margin-top: 8px; padding: 8px; background-color: #f0f9ff; border: 2px solid #3b82f6; border-radius: 6px; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);">
                                <span style="font-size: 12px; color: #1e40af; font-weight: bold;">💾 Debug newColorIntensity: </span>
                                <span id="debugImageNewColorIntensity" style="font-size: 12px; color: #1e40af; font-weight: bold;">N/A</span>
                            </div>
                        </div>

                        <!-- Image Stroke Controls -->
                        <div class="control-group">
                            <label for="iImageStroke">Stroke:</label>
                            <select id="iImageStroke">
                                <option value="none" selected>No Stroke</option>
                                <option value="standard">Standard</option>
                            </select>
                        </div>

                        <!-- Standard Stroke Controls -->
                        <div class="parameter-control image-stroke-param" style="display: none;">
                            <h4>Standard Stroke</h4>
                            <div class="control-group">
                                <label for="iImageStrokeWidth">Width:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageStrokeWidth" min="1" max="50" value="3" step="1">
                                    <span class="slider-value" id="vImageStrokeWidth">3px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageStrokeColor">Color:</label>
                                <div class="simplified-color-picker">
                                    <input type="color" id="iImageStrokeColor" value="#000000">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageStrokeOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageStrokeOpacity" min="0" max="100" value="100" step="1">
                                    <span class="slider-value" id="vImageStrokeOpacity">100%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Image Shadow Controls -->
                        <div class="control-group">
                            <label for="iImageShadow">Shadow:</label>
                            <select id="iImageShadow">
                                <option value="none" selected>No Shadow</option>
                                <option value="standard">Standard</option>
                            </select>
                        </div>

                        <!-- Standard Shadow Controls -->
                        <div class="parameter-control image-shadow-param" style="display: none;">
                            <h4>Standard Shadow</h4>
                            <div class="control-group">
                                <label for="iImageShadowColor">Color:</label>
                                <div class="simplified-color-picker">
                                    <input type="color" id="iImageShadowColor" value="#000000">
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOpacity">Opacity:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOpacity" min="0" max="100" value="100" step="1">
                                    <span class="slider-value" id="vImageShadowOpacity">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOffsetX">Offset X:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOffsetX" min="-50" max="50" value="5" step="1">
                                    <span class="slider-value" id="vImageShadowOffsetX">5px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowOffsetY">Offset Y:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowOffsetY" min="-50" max="50" value="5" step="1">
                                    <span class="slider-value" id="vImageShadowOffsetY">5px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageShadowBlur">Blur:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageShadowBlur" min="0" max="50" value="10" step="1">
                                    <span class="slider-value" id="vImageShadowBlur">10px</span>
                                </div>
                            </div>
                        </div>

                        <!-- Image Masking Controls -->
                        <div class="control-group" id="imageMaskingGroup" style="display: none;">
                            <button id="maskImageBtn" class="professional-btn" style="margin-bottom: 8px;">
                                <i class="fas fa-shapes"></i> Mask with Shape
                            </button>
                            <button id="unmaskImageBtn" class="professional-btn" style="display: none;">
                                <i class="fas fa-times"></i> Remove Mask
                            </button>
                        </div>

                        <!-- CSS Filters Section -->
                        <div class="image-properties-header" style="margin-top: 20px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #e5e7eb; font-family: 'Inter', sans-serif; font-weight: 600; font-size: 0.95rem; color: #374151;">
                            CSS Filters
                        </div>
                        <div id="image-filters-controls">
                            <!-- Filter Presets Dropdown -->
                            <div class="control-group">
                                <label for="filterPresetSelect">Filter Presets:</label>
                                <div style="display: flex; gap: 5px; margin-top: 5px;">
                                    <select id="filterPresetSelect" style="flex: 1; padding: 6px 8px; font-size: 12px; border: 1px solid #ddd; border-radius: 3px; background: white;">
                                        <option value="">Select a preset...</option>
                                        <option value="watercolor">Watercolor</option>
                                        <option value="faded-photo">Faded Photo</option>
                                        <option value="old-horror">Old Horror</option>
                                        <option value="old-grainy">Old Grainy</option>
                                        <option value="fade-out">Fade Out</option>
                                        <option value="mist">Mist</option>
                                    </select>
                                    <button id="deletePresetBtn" title="Delete selected preset" style="padding: 6px 10px; font-size: 12px; border: 1px solid #dc3545; border-radius: 3px; background: #dc3545; color: white; cursor: pointer; display: none;">
                                        🗑️
                                    </button>
                                </div>
                            </div>

                            <!-- Individual Filter Controls -->
                            <div class="control-group">
                                <label for="iImageBlur">Blur:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageBlur" min="0" max="10" value="0" step="0.1">
                                    <span class="slider-value" id="vImageBlur">0px</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageBrightness">Brightness:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageBrightness" min="0" max="3" value="1" step="0.1">
                                    <span class="slider-value" id="vImageBrightness">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageContrast">Contrast:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageContrast" min="0" max="3" value="1" step="0.1">
                                    <span class="slider-value" id="vImageContrast">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageSaturation">Saturation:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageSaturation" min="0" max="3" value="1" step="0.1">
                                    <span class="slider-value" id="vImageSaturation">100%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageHue">Hue Rotate:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageHue" min="0" max="360" value="0" step="1">
                                    <span class="slider-value" id="vImageHue">0°</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageGrayscale">Grayscale:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageGrayscale" min="0" max="1" value="0" step="0.1">
                                    <span class="slider-value" id="vImageGrayscale">0%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageSepia">Sepia:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageSepia" min="0" max="1" value="0" step="0.1">
                                    <span class="slider-value" id="vImageSepia">0%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <label for="iImageInvert">Invert:</label>
                                <div class="slider-container">
                                    <input type="range" id="iImageInvert" min="0" max="1" value="0" step="0.1">
                                    <span class="slider-value" id="vImageInvert">0%</span>
                                </div>
                            </div>
                            <div class="control-group">
                                <button id="resetImageFiltersBtn" class="reset-filters-btn" style="width: 100%; padding: 8px; background-color: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                                    Reset All Filters
                                </button>
                            </div>

                            <!-- Save Filter Preset -->
                            <div class="control-group" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e5e7eb;">
                                <label for="newPresetName" style="font-weight: 600; color: #374151;">Save New Preset:</label>
                                <input type="text" id="newPresetName" placeholder="Enter preset name (e.g., Nuclear)" style="width: 100%; padding: 6px 8px; font-size: 12px; border: 1px solid #ddd; border-radius: 3px; margin-top: 5px; margin-bottom: 8px; font-family: 'Inter', sans-serif;">
                                <button id="saveFilterPresetBtn" class="save-preset-btn" style="width: 100%; padding: 8px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-family: 'Inter', sans-serif; font-weight: 500; transition: background-color 0.2s;">
                                    💾 Save Filter Preset
                                </button>
                            </div>
                            <div class="control-group">
                                <button id="testImageFiltersBtn" class="test-filters-btn" style="width: 100%; padding: 8px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 5px;">
                                    Test Filters (Grayscale)
                                </button>
                            </div>
                            <div class="control-group">
                                <button id="debugImageFiltersBtn" class="debug-filters-btn" style="width: 100%; padding: 8px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 5px;">
                                    Debug: Check Selection
                                </button>
                            </div>
                        </div>

                        <!-- Image Actions Section -->
                        <div class="image-actions-header" style="margin-top: 20px; margin-bottom: 12px; padding-bottom: 8px; border-bottom: 1px solid #e5e7eb; font-family: 'Inter', sans-serif; font-weight: 600; font-size: 0.95rem; color: #374151;">
                            Image Actions
                        </div>

                        <div class="image-actions-container">
                            <!-- Remove Background Button -->
                            <button id="removeBgBtn" class="professional-btn" style="display: none;">
                                <i class="fas fa-cut"></i> Remove Background
                            </button>

                            <!-- Regenerate Button -->
                            <button id="regenerateBtn" class="professional-btn" style="display: none;">
                                <i class="fas fa-redo"></i> Regenerate
                            </button>

                            <!-- Restyle Button -->
                            <button id="restyleBtn" class="professional-btn" style="display: none;">
                                <i class="fas fa-magic"></i> Restyle
                            </button>

                            <!-- Replace Button -->
                            <button id="replaceBtn" class="professional-btn" style="display: none;">
                                <i class="fas fa-exchange-alt"></i> Replace
                            </button>
                        </div>

                        <!-- Template ID for image replacement -->
                        <div class="control-group" style="border-top: 2px solid #e74c3c; margin-top: 15px; padding-top: 15px;">
                            <label for="imageTemplateId" style="color: #e74c3c; font-weight: bold;">Template ID:</label>
                            <select id="imageTemplateId" style="border: 2px solid #e74c3c; background-color: #fff5f5;" disabled>
                                <option value="">Not Replaceable</option>
                                <option value="i01">i01 - Main Image</option>
                                <option value="i02">i02 - Image 2</option>
                                <option value="i03">i03 - Image 3</option>
                                <option value="i04">i04 - Image 4</option>
                                <option value="i05">i05 - Image 5</option>
                                <optgroup label="🎭 Mask Shapes">
                                    <option value="mask01">mask01 - Mask Shape 1</option>
                                    <option value="mask02">mask02 - Mask Shape 2</option>
                                    <option value="mask03">mask03 - Mask Shape 3</option>
                                </optgroup>
                            </select>
                            <small style="color: #7f8c8d; font-size: 11px; display: block; margin-top: 5px;">
                                Select an ID to make this image replaceable in templates. i01 is the main image that gets replaced with generated content.
                            </small>
                        </div>
                        <!-- Debug display for persistent Template ID (Images) -->
                            <div style="margin-top: 5px; padding: 5px; background-color: #fef2f2; border: 1px solid #e74c3c; border-radius: 4px;">
                                <span style="font-size: 10px; color: #dc2626; font-weight: bold;">Debug newTemplateId: </span>
                                <span id="debugImageNewTemplateId" style="font-size: 10px; color: #dc2626;">N/A</span>
                            </div>
                    </div>
                     <p id="no-image-selected-msg">No image selected.</p>
                </div><!-- End Image Tab -->

                <!-- ADMIN TAB CONTENT -->
                <div class="sidebar-content" id="admin-tab-content">
                    <div class="admin-properties-header" style="font-size: 1.1em; font-weight: 600; margin-bottom: 15px; color: #1e293b; border-bottom: 1px solid #e2e8f0; padding-bottom: 8px;">Inspiration Details</div>
                    <div id="admin-controls" style="display: flex; flex-direction: column; gap: 12px;">
                        <div class="control-group">
                            <label for="adminImageUrl" style="flex-basis: 80px; text-align: left;">Image URL:</label>
                            <input id="adminImageUrl" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                        <div class="control-group">
                            <label for="adminModel" style="flex-basis: 80px; text-align: left;">Model:</label>
                            <input id="adminModel" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>
                         <div class="control-group">
                            <label for="adminPalette" style="flex-basis: 80px; text-align: left;">Palette:</label>
                            <input id="adminPalette" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>

                        <div class="control-group">
                            <label for="adminOriginalPalette" style="flex-basis: 80px; text-align: left;">Original Palette:</label>
                            <input id="adminOriginalPalette" type="text" readonly style="background-color: #e9ecef; flex-grow: 1;">
                        </div>

                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminOriginalObject" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Original Object:</label>
                            <textarea id="adminOriginalObject" rows="3" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical;"></textarea>
                        </div>

                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminFontStyles" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Text Font Styles:</label>
                            <textarea id="adminFontStyles" rows="4" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical; font-family: monospace;" placeholder="No saved font styles"></textarea>
                            <small style="color: #666; font-size: 10px; margin-top: 2px;">JSON array of saved font configurations for cycling</small>
                        </div>

                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminDecorStyles" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Decor Styles:</label>
                            <textarea id="adminDecorStyles" rows="4" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical; font-family: monospace;" placeholder="No saved decor styles"></textarea>
                            <small style="color: #666; font-size: 10px; margin-top: 2px;">JSON array of saved decoration and shadow configurations for cycling</small>
                            <button type="button" onclick="
                                const field = document.getElementById('adminFontStyles');
                                console.log('🎯 🔧 MANUAL TEST - Checking ALL data sources:');
                                console.log('🎯 🔧 window.fontStylesList:', window.fontStylesList);
                                console.log('🎯 🔧 window._fontStylesListBackup:', window._fontStylesListBackup);
                                console.log('🎯 🔧 window.__fontStylesData__:', window.__fontStylesData__);

                                let dataToUse = null;
                                let source = '';

                                // Try main location
                                if (window.fontStylesList && window.fontStylesList.length > 0) {
                                    dataToUse = window.fontStylesList;
                                    source = 'window.fontStylesList';
                                }
                                // Try backup 1
                                else if (window._fontStylesListBackup && window._fontStylesListBackup.length > 0) {
                                    dataToUse = window._fontStylesListBackup;
                                    source = 'window._fontStylesListBackup';
                                }
                                // Try backup 2
                                else if (window.__fontStylesData__ && window.__fontStylesData__.length > 0) {
                                    dataToUse = window.__fontStylesData__;
                                    source = 'window.__fontStylesData__';
                                }
                                // Try sessionStorage backup
                                else {
                                    try {
                                        const sessionData = sessionStorage.getItem('_fontStylesBackup');
                                        if (sessionData) {
                                            dataToUse = JSON.parse(sessionData);
                                            source = 'sessionStorage';
                                        }
                                    } catch (e) {
                                        console.log('🎯 🔧 SessionStorage backup failed:', e);
                                    }
                                }
                                // Try DOM element backup
                                if (!dataToUse) {
                                    try {
                                        const domStorage = document.getElementById('_fontStylesStorage');
                                        if (domStorage) {
                                            const domData = domStorage.getAttribute('data-fontstyles');
                                            if (domData) {
                                                dataToUse = JSON.parse(domData);
                                                source = 'DOM element';
                                            }
                                        }
                                    } catch (e) {
                                        console.log('🎯 🔧 DOM backup failed:', e);
                                    }
                                }

                                if (dataToUse && dataToUse.length > 0) {
                                    field.value = JSON.stringify(dataToUse, null, 2);
                                    console.log('🎯 🔧 MANUAL TEST: Found data in ' + source + ', set field value');
                                    // Restore to main location
                                    window.fontStylesList = dataToUse;
                                } else {
                                    field.value = 'TEST DATA: No fontStylesList found in ANY backup location';
                                    console.log('🎯 🔧 MANUAL TEST: No fontStylesList found in ANY location');
                                }
                            " style="margin-top: 5px; padding: 4px 8px; font-size: 12px; background-color: #6366f1; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🔧 Test Fill Field
                            </button>
                            <button type="button" onclick="
                                if (window.updateAdminFontStylesDisplay) {
                                    window.updateAdminFontStylesDisplay();
                                    console.log('🎯 ✅ Called updateAdminFontStylesDisplay function');
                                } else {
                                    console.log('🎯 ❌ updateAdminFontStylesDisplay function not available');
                                }
                            " style="margin-top: 5px; padding: 4px 8px; font-size: 12px; background-color: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🔄 Call Update Function
                            </button>
                            <button type="button" onclick="
                                console.log('🛡️ PROTECTION TEST - Checking protection system...');
                                if (window._fontStylesProtection) {
                                    const data = window._fontStylesProtection.retrieve();
                                    if (data && data.length > 0) {
                                        const field = document.getElementById('adminFontStyles');
                                        field.value = JSON.stringify(data, null, 2);
                                        console.log('🛡️ PROTECTION TEST: Restored', data.length, 'font styles from protection system');
                                        window.fontStylesList = data;
                                    } else {
                                        console.log('🛡️ PROTECTION TEST: No protected data found');
                                    }
                                } else {
                                    console.log('🛡️ PROTECTION TEST: Protection system not available');
                                }
                            " style="margin-top: 5px; padding: 4px 8px; font-size: 12px; background-color: #ef4444; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                🛡️ Test Protection
                            </button>
                        </div>

                        <div class="control-group" style="flex-direction: column; align-items: flex-start;">
                            <label for="adminPrompt" style="flex-basis: auto; text-align: left; margin-bottom: 5px;">Prompt:</label>
                            <textarea id="adminPrompt" rows="8" readonly style="background-color: #e9ecef; width: 100%; border: 1px solid #d1d5db; border-radius: 6px; padding: 6px 10px; font-size: 0.9em; resize: vertical;"></textarea>
                        </div>
                        <input type="hidden" id="adminInspirationId"> <!-- Hidden field for ID -->

                        <!-- Template Save Buttons - Show different buttons based on whether editing existing template -->
                        <div id="templateSaveButtons">
                            <button id="saveTemplateBtn" class="add-btn" style="margin-top: 15px;">Save as Inspiration</button> <!-- Default for new templates -->
                            <button id="updateTemplateBtn" class="add-btn" style="margin-top: 15px; display: none; background-color: #10b981;">💾 Save</button> <!-- For existing templates -->
                            <button id="saveAsTemplateBtn" class="add-btn" style="margin-top: 10px; display: none;">📋 Save As New</button> <!-- For existing templates -->
                        </div>

                        <button id="saveTextStyleBtn" class="add-btn" style="margin-top: 10px;">Save Text Style</button> <!-- New Text Style button -->
                         <button id="validateMasksBtn" class="add-btn" style="margin-top: 10px; background-color: #6366f1;">🎭 Validate Masks</button> <!-- Debug button for mask validation -->
                    </div>
                </div><!-- End Admin Tab -->

            </aside>
        </main>
    </div>

    <!-- Color input replaced with text input for Coloris -->
    <!-- OpenType.js for Grid Distort effect -->
    <script src="https://cdn.jsdelivr.net/npm/opentype.js@1.3.4/dist/opentype.min.js"></script>

    <script src="/js/font-variant-detector.js"></script>
    <script src="/js/gradient-color-picker.js"></script>
    <script src="/js/decoration-module.js"></script>
    <script type="module">
        // Load color palette functions and make them globally available
        import { getPaletteById, getTextColorForPalette } from '/js/data/colorPalettes.js';
        import { setupProjectModal } from '/js/components/ProjectModal.js?v=1749502000';

        window.getPaletteById = getPaletteById;
        window.getTextColorForPalette = getTextColorForPalette;

        // Setup project modal functionality
        setupProjectModal();
    </script>
    <script src="/js/design-editor.js?v=1750256000"></script>
    <script src="/js/mesh-warp-implementation.js?v=1750012021"></script>
    <script src="/js/left-menu.js?v=1750012000"></script>
    <script src="/js/shapes-api.js"></script>
    <script src="/js/elements-accordion.js"></script>
    <script src="/js/images-loader.js"></script>
    <script type="module" src="/js/components/Topbar.js"></script>
    <script type="module" src="/js/components/CollectionModal.js"></script>
    <script type="module" src="/js/components/ColorPaletteSelector.js"></script>
    <script type="module">
       import { createTopbar } from '/js/components/Topbar.js';

       document.addEventListener('DOMContentLoaded', async () => {
           // Initialize topbar (same as other pages)
           await createTopbar();

           // Initialize font variant detection system
           if (window.fontVariantDetector && window.fontMap) {
               console.log('🔤 Initializing font variant detection system...');
               try {
                   window.fontVariantDetector.initialize(window.fontMap);
                   console.log('🔤 Font variant detection system initialized successfully');
               } catch (error) {
                   console.error('🔤 Failed to initialize font variant detection system:', error);
               }
           } else {
               console.warn('🔤 FontVariantDetector or fontMap not available');
           }

           // Wait for design-editor.js to load before loading template data
           const waitForDesignEditor = () => {
               return new Promise((resolve) => {
                   if (window.updateAdminFontStylesDisplay) {
                       console.log('🎯 ✅ design-editor.js already loaded');
                       resolve();
                   } else {
                       console.log('🎯 ⏳ Waiting for design-editor.js to load...');
                       const checkInterval = setInterval(() => {
                           if (window.updateAdminFontStylesDisplay) {
                               console.log('🎯 ✅ design-editor.js loaded');
                               clearInterval(checkInterval);
                               resolve();
                           }
                       }, 100);
                   }
               });
           };

           await waitForDesignEditor();

           // Initialize template-specific flags
           window._templateSpecificFontStylesLoaded = false;
           window._templateSpecificDecorStylesLoaded = false;

           // Load template data from sessionStorage (from inspiration.html)
           loadTemplateFromSessionStorage();

           // AI Generator functionality is handled by left-menu.js

           // ULTRA ROBUST FONT STYLES PROTECTION SYSTEM
           // Create multiple protected storage locations for font styles data
           window._fontStylesProtection = {
               data: null,
               isProtected: false,

               // Store font styles data in multiple protected locations
               protect: function(fontStylesData) {
                   if (!fontStylesData || !Array.isArray(fontStylesData) || fontStylesData.length === 0) {
                       console.log('🛡️ No font styles data to protect');
                       return;
                   }

                   console.log('🛡️ PROTECTING font styles data:', fontStylesData.length, 'styles');
                   this.data = JSON.parse(JSON.stringify(fontStylesData)); // Deep clone
                   this.isProtected = true;

                   // Store in multiple locations
                   window._PROTECTED_FONT_STYLES_ = JSON.parse(JSON.stringify(fontStylesData));
                   sessionStorage.setItem('_PROTECTED_FONT_STYLES_', JSON.stringify(fontStylesData));
                   localStorage.setItem('_PROTECTED_FONT_STYLES_BACKUP_', JSON.stringify(fontStylesData));

                   // Create hidden DOM element as backup
                   let hiddenStorage = document.getElementById('_hiddenFontStylesStorage');
                   if (!hiddenStorage) {
                       hiddenStorage = document.createElement('div');
                       hiddenStorage.id = '_hiddenFontStylesStorage';
                       hiddenStorage.style.display = 'none';
                       document.body.appendChild(hiddenStorage);
                   }
                   hiddenStorage.setAttribute('data-font-styles', JSON.stringify(fontStylesData));

                   console.log('🛡️ Font styles data protected in 4 locations');
                   this.startMonitoring();
               },

               // Retrieve font styles data from any available location
               retrieve: function() {
                   let data = null;
                   let source = '';

                   // Try main storage
                   if (this.data && this.data.length > 0) {
                       data = this.data;
                       source = 'main protection';
                   }
                   // Try window backup
                   else if (window._PROTECTED_FONT_STYLES_ && window._PROTECTED_FONT_STYLES_.length > 0) {
                       data = window._PROTECTED_FONT_STYLES_;
                       source = 'window backup';
                   }
                   // Try sessionStorage
                   else {
                       try {
                           const sessionData = sessionStorage.getItem('_PROTECTED_FONT_STYLES_');
                           if (sessionData) {
                               data = JSON.parse(sessionData);
                               source = 'sessionStorage';
                           }
                       } catch (e) {
                           console.log('🛡️ SessionStorage retrieval failed:', e);
                       }
                   }
                   // Try localStorage
                   if (!data) {
                       try {
                           const localData = localStorage.getItem('_PROTECTED_FONT_STYLES_BACKUP_');
                           if (localData) {
                               data = JSON.parse(localData);
                               source = 'localStorage';
                           }
                       } catch (e) {
                           console.log('🛡️ LocalStorage retrieval failed:', e);
                       }
                   }
                   // Try DOM element
                   if (!data) {
                       try {
                           const hiddenStorage = document.getElementById('_hiddenFontStylesStorage');
                           if (hiddenStorage) {
                               const domData = hiddenStorage.getAttribute('data-font-styles');
                               if (domData) {
                                   data = JSON.parse(domData);
                                   source = 'DOM element';
                               }
                           }
                       } catch (e) {
                           console.log('🛡️ DOM retrieval failed:', e);
                       }
                   }

                   if (data && data.length > 0) {
                       console.log('🛡️ Retrieved font styles from:', source, '- Count:', data.length);
                       return data;
                   }

                   console.log('🛡️ No font styles data found in any protected location');
                   return null;
               },

               // Start monitoring the adminFontStyles field
               startMonitoring: function() {
                   if (this.monitorInterval) {
                       clearInterval(this.monitorInterval);
                   }

                   console.log('🛡️ Starting font styles field monitoring...');

                   // Add delay if template loading is in progress
                   if (window._templateAlreadyLoaded === undefined) {
                       console.log('🛡️ Template loading may be in progress, delaying monitoring...');
                       setTimeout(() => {
                           this.startMonitoring();
                       }, 2000);
                       return;
                   }

                   let checkCount = 0;

                   this.monitorInterval = setInterval(() => {
                       checkCount++;
                       const adminFontStylesField = document.getElementById('adminFontStyles');

                       if (adminFontStylesField) {
                           const currentValue = adminFontStylesField.value;
                           const hasData = currentValue && currentValue.trim() !== '' && currentValue !== 'No saved font styles';

                           console.log(`🛡️ FONT PROTECTION CHECK ${checkCount}:`, {
                               hasData,
                               isProtected: this.isProtected,
                               templateSpecificLoaded: window._templateSpecificFontStylesLoaded,
                               templateAlreadyLoaded: window._templateAlreadyLoaded,
                               currentValue: currentValue?.substring(0, 50) + '...'
                           });

                           // Check if template data is already loaded (either flag is set or field has non-empty array data)
                           const templateDataLoaded = window._templateSpecificFontStylesLoaded ||
                                                    window._templateAlreadyLoaded ||
                                                    (hasData && currentValue !== '[]' && !currentValue.includes('No saved font styles'));

                           if (!hasData && this.isProtected && !templateDataLoaded) {
                               const protectedData = this.retrieve();
                               if (protectedData && protectedData.length > 0) {
                                   const jsonValue = JSON.stringify(protectedData, null, 2);
                                   adminFontStylesField.value = jsonValue;
                                   console.log(`🛡️ RESTORED font styles to field (check ${checkCount}) - no template data loaded`);

                                   // Also restore to window.fontStylesList if needed
                                   if (!window.fontStylesList || window.fontStylesList.length === 0) {
                                       window.fontStylesList = JSON.parse(JSON.stringify(protectedData));
                                       console.log('🛡️ Restored window.fontStylesList');
                                   }
                               }
                           } else if (templateDataLoaded) {
                               console.log(`🛡️ SKIPPING font styles restoration (check ${checkCount}) - template data already loaded`);

                               // Stop monitoring if template data is loaded and we have data in the field
                               if (hasData && checkCount > 10) {
                                   console.log('🛡️ STOPPING font styles monitoring - template data confirmed loaded');
                                   clearInterval(this.monitorInterval);
                                   this.monitorInterval = null;
                                   return;
                               }
                           }
                       }

                       // Stop monitoring after 2 minutes
                       if (checkCount >= 240) { // 240 * 500ms = 2 minutes
                           console.log('🛡️ Stopping font styles monitoring after 2 minutes');
                           clearInterval(this.monitorInterval);
                       }
                   }, 500); // Check every 500ms
               }
           };

           // DECOR STYLES PROTECTION SYSTEM
           // Create multiple protected storage locations for decor styles data
           window._decorStylesProtection = {
               data: null,
               isProtected: false,

               // Store decor styles data in multiple protected locations
               protect: function(decorStylesData) {
                   if (!decorStylesData || !Array.isArray(decorStylesData) || decorStylesData.length === 0) {
                       console.log('🛡️ No decor styles data to protect');
                       return;
                   }

                   console.log('🛡️ PROTECTING decor styles data:', decorStylesData.length, 'styles');
                   this.data = JSON.parse(JSON.stringify(decorStylesData)); // Deep clone
                   this.isProtected = true;

                   // Store in multiple locations
                   window._PROTECTED_DECOR_STYLES_ = JSON.parse(JSON.stringify(decorStylesData));

                   // Store in localStorage
                   try {
                       localStorage.setItem('_PROTECTED_DECOR_STYLES_BACKUP_', JSON.stringify(decorStylesData));
                   } catch (e) {
                       console.log('🛡️ localStorage storage failed:', e);
                   }

                   // Store in hidden DOM element
                   let hiddenStorage = document.getElementById('_hiddenDecorStylesStorage');
                   if (!hiddenStorage) {
                       hiddenStorage = document.createElement('div');
                       hiddenStorage.id = '_hiddenDecorStylesStorage';
                       hiddenStorage.style.display = 'none';
                       document.body.appendChild(hiddenStorage);
                   }
                   hiddenStorage.setAttribute('data-decor-styles', JSON.stringify(decorStylesData));

                   console.log('🛡️ Decor styles data protected in 4 locations');
                   this.startMonitoring();
               },

               // Retrieve decor styles data from any available location
               retrieve: function() {
                   let data = null;
                   let source = '';

                   // Try main storage
                   if (this.data && this.data.length > 0) {
                       data = this.data;
                       source = 'main storage';
                   }

                   // Try window backup
                   if (!data && window._PROTECTED_DECOR_STYLES_) {
                       try {
                           data = JSON.parse(JSON.stringify(window._PROTECTED_DECOR_STYLES_));
                           source = 'window backup';
                       } catch (e) {
                           console.log('🛡️ Window backup retrieval failed:', e);
                       }
                   }

                   // Try localStorage
                   if (!data) {
                       try {
                           const stored = localStorage.getItem('_PROTECTED_DECOR_STYLES_BACKUP_');
                           if (stored) {
                               data = JSON.parse(stored);
                               source = 'localStorage';
                           }
                       } catch (e) {
                           console.log('🛡️ localStorage retrieval failed:', e);
                       }
                   }

                   // Try DOM storage
                   if (!data) {
                       try {
                           const hiddenStorage = document.getElementById('_hiddenDecorStylesStorage');
                           if (hiddenStorage) {
                               const stored = hiddenStorage.getAttribute('data-decor-styles');
                               if (stored) {
                                   data = JSON.parse(stored);
                                   source = 'DOM storage';
                               }
                           }
                       } catch (e) {
                           console.log('🛡️ DOM retrieval failed:', e);
                       }
                   }

                   if (data && data.length > 0) {
                       console.log('🛡️ Retrieved decor styles from:', source, '- Count:', data.length);
                       return data;
                   }

                   console.log('🛡️ No decor styles data found in any protected location');
                   return null;
               },

               // Start monitoring the adminDecorStyles field
               startMonitoring: function() {
                   // Stop monitoring if system is fully loaded
                   if (window.isFullyLoaded) {
                       console.log('🛡️ System fully loaded - stopping decor styles monitoring');
                       return;
                   }

                   if (this.monitorInterval) {
                       clearInterval(this.monitorInterval);
                   }

                   console.log('🛡️ Starting decor styles field monitoring...');

                   // Add delay if template loading is in progress (but limit recursion)
                   if (window._templateAlreadyLoaded === undefined && !this.recursionCount) {
                       this.recursionCount = (this.recursionCount || 0) + 1;
                       if (this.recursionCount < 5) { // Limit to 5 attempts
                           console.log('🛡️ Template loading may be in progress, delaying monitoring... (attempt', this.recursionCount, ')');
                           setTimeout(() => {
                               this.startMonitoring();
                           }, 2000);
                           return;
                       } else {
                           console.log('🛡️ Max recursion attempts reached, proceeding with monitoring');
                           this.recursionCount = 0;
                       }
                   }

                   let checkCount = 0;

                   this.monitorInterval = setInterval(() => {
                       checkCount++;
                       const adminDecorStylesField = document.getElementById('adminDecorStyles');

                       if (adminDecorStylesField) {
                           const currentValue = adminDecorStylesField.value;
                           const hasData = currentValue && currentValue.trim() !== '' && currentValue !== 'No saved decor styles';

                           // Check if template data is already loaded (either flag is set or field has non-empty array data)
                           const templateDataLoaded = window._templateSpecificDecorStylesLoaded ||
                                                    window._templateAlreadyLoaded ||
                                                    (hasData && currentValue !== '[]' && !currentValue.includes('No saved decor styles'));

                           if (!hasData && this.isProtected && !templateDataLoaded) {
                               const protectedData = this.retrieve();
                               if (protectedData && protectedData.length > 0) {
                                   const jsonValue = JSON.stringify(protectedData, null, 2);
                                   adminDecorStylesField.value = jsonValue;
                                   console.log(`🛡️ RESTORED decor styles to field (check ${checkCount}) - no template data loaded`);

                                   // Also restore to window.decorStylesList if needed
                                   if (!window.decorStylesList || window.decorStylesList.length === 0) {
                                       window.decorStylesList = JSON.parse(JSON.stringify(protectedData));
                                       console.log('🛡️ Restored window.decorStylesList');
                                   }
                               }
                           } else if (templateDataLoaded) {
                               console.log(`🛡️ SKIPPING decor styles restoration (check ${checkCount}) - template data already loaded`);

                               // Stop monitoring if template data is loaded and we have data in the field
                               if (hasData && checkCount > 10) {
                                   console.log('🛡️ STOPPING decor styles monitoring - template data confirmed loaded');
                                   clearInterval(this.monitorInterval);
                                   this.monitorInterval = null;
                                   return;
                               }
                           }
                       }

                       // Stop monitoring after 2 minutes
                       if (checkCount >= 240) { // 240 * 500ms = 2 minutes
                           console.log('🛡️ Stopping decor styles monitoring after 2 minutes');
                           clearInterval(this.monitorInterval);
                       }
                   }, 500); // Check every 500ms
               }
           };

           // Additional check to ensure font styles are displayed after all scripts load
           setTimeout(() => {
               console.log('🎯 🔄 FINAL CHECK: Looking for font styles data...');

               // Check if we have font styles data in any location
               let fontStylesData = null;

               if (window.fontStylesList && window.fontStylesList.length > 0) {
                   fontStylesData = window.fontStylesList;
                   console.log('🎯 Found font styles in window.fontStylesList');
               } else if (window._fontStylesProtection && !window._templateSpecificFontStylesLoaded) {
                   // Only use protection system if no template-specific data was loaded
                   fontStylesData = window._fontStylesProtection.retrieve();
                   if (fontStylesData) {
                       console.log('🎯 Found font styles in protected storage (no template data)');
                   }
               }

               if (fontStylesData && fontStylesData.length > 0) {
                   // Protect the data
                   window._fontStylesProtection.protect(fontStylesData);

                   const adminFontStylesField = document.getElementById('adminFontStyles');
                   if (adminFontStylesField && (!adminFontStylesField.value || adminFontStylesField.value.trim() === '')) {
                       console.log('🎯 🔄 FINAL CHECK: adminFontStyles field is empty but we have font styles data');
                       if (window.updateAdminFontStylesDisplay) {
                           window.updateAdminFontStylesDisplay();
                           console.log('🎯 ✅ FINAL CHECK: Called updateAdminFontStylesDisplay');
                       } else {
                           // Direct population as fallback
                           const jsonValue = JSON.stringify(fontStylesData, null, 2);
                           adminFontStylesField.value = jsonValue;
                           console.log('🎯 ✅ FINAL CHECK: Directly populated adminFontStyles field');
                       }
                   }
               } else {
                   console.log('🎯 ❌ FINAL CHECK: No font styles data found anywhere');
               }

               // Similar check for decor styles
               console.log('🎯 🔄 FINAL CHECK: Looking for decor styles data...');

               let decorStylesData = null;

               if (window.decorStylesList && window.decorStylesList.length > 0) {
                   decorStylesData = window.decorStylesList;
                   console.log('🎯 Found decor styles in window.decorStylesList');
               } else if (window._decorStylesProtection && !window._templateSpecificDecorStylesLoaded) {
                   // Only use protection system if no template-specific data was loaded
                   decorStylesData = window._decorStylesProtection.retrieve();
                   if (decorStylesData) {
                       console.log('🎯 Found decor styles in protected storage (no template data)');
                   }
               }

               if (decorStylesData && decorStylesData.length > 0) {
                   // Protect the data
                   window._decorStylesProtection.protect(decorStylesData);

                   const adminDecorStylesField = document.getElementById('adminDecorStyles');
                   if (adminDecorStylesField && (!adminDecorStylesField.value || adminDecorStylesField.value.trim() === '')) {
                       console.log('🎯 🔄 FINAL CHECK: adminDecorStyles field is empty but we have decor styles data');
                       if (window.updateAdminDecorStylesDisplay) {
                           window.updateAdminDecorStylesDisplay();
                           console.log('🎯 ✅ FINAL CHECK: Called updateAdminDecorStylesDisplay');
                       } else {
                           // Direct population as fallback
                           const jsonValue = JSON.stringify(decorStylesData, null, 2);
                           adminDecorStylesField.value = jsonValue;
                           console.log('🎯 ✅ FINAL CHECK: Directly populated adminDecorStyles field');
                       }
                   }
               } else {
                   console.log('🎯 ❌ FINAL CHECK: No decor styles data found anywhere');
               }
           }, 3000); // Wait 3 seconds for all scripts to load
       });

       // Function to load template data from sessionStorage and populate admin fields
       function loadTemplateFromSessionStorage() {
           try {
               // Check if a template was already loaded via URL parameter
               if (window._templateAlreadyLoaded) {
                   console.log('🎯 📋 TEMPLATE LOADING - Template already loaded via URL, skipping sessionStorage load');
                   return;
               }

               // Initialize template-specific flags at the start of each load
               window._templateSpecificFontStylesLoaded = false;
               window._templateSpecificDecorStylesLoaded = false;
               console.log('🎯 📋 TEMPLATE LOADING - Reset template-specific flags');

               const modifiedTemplateData = sessionStorage.getItem('modifiedTemplate');
               if (modifiedTemplateData) {
                   const template = JSON.parse(modifiedTemplateData);

                   // Reset template-specific flags for new template load
                   window._templateSpecificFontStylesLoaded = false;
                   window._templateSpecificDecorStylesLoaded = false;
                   console.log('🎯 📋 TEMPLATE LOADING - Template found, reset flags to false');

                   // Debug: Log the raw sessionStorage data
                   console.log('🎯 Raw sessionStorage modifiedTemplate:', modifiedTemplateData.substring(0, 500) + '...');
                   console.log('🎯 Parsed template keys:', Object.keys(template));
                   console.log('🎯 Template adminData keys:', template.adminData ? Object.keys(template.adminData) : 'no adminData');
                   console.log('🎯 📥 LOADING template data from sessionStorage:', {
                       templateId: template._id,
                       templateName: template.name,
                       'template keys': Object.keys(template),
                       'adminData exists': !!template.adminData,
                       'adminData keys': template.adminData ? Object.keys(template.adminData) : 'no adminData',
                       'adminData.object': template.adminData?.object,
                       'adminData.palette': template.adminData?.palette,
                       'top-level fontStylesList exists': !!template.fontStylesList,
                       'adminData.fontStylesList exists': !!template.adminData?.fontStylesList,
                       'top-level fontStylesList': template.fontStylesList,
                       'adminData.fontStylesList': template.adminData?.fontStylesList,
                       'top-level fontStylesList length': template.fontStylesList?.length || 0,
                       'adminData.fontStylesList length': template.adminData?.fontStylesList?.length || 0,
                       'originalPalette': template.originalPalette
                   });

                   // Populate admin fields with the template's ORIGINAL data (CORRECTED field IDs)
                   const editOriginalObjectField = document.getElementById('editOriginalObject');
                   const editOriginalPaletteField = document.getElementById('editOriginalPalette');

                   if (editOriginalObjectField && template.originalObject) {
                       editOriginalObjectField.value = template.originalObject;
                       console.log('🎯 Set Original Object field from template.originalObject:', template.originalObject);
                   }

                   if (editOriginalPaletteField && template.originalPalette) {
                       editOriginalPaletteField.value = template.originalPalette;
                       console.log('🎯 Set Original Palette field from template.originalPalette:', template.originalPalette);
                   }

                   // Load font styles list if available (check both top-level and adminData) - CRITICAL: Check for content, not just existence
                   console.log('🎯 🔍 FONT STYLES LOADING - Checking for font styles...');
                   console.log('🎯 🔍 template.fontStylesList:', template.fontStylesList);
                   console.log('🎯 🔍 template.adminData?.fontStylesList:', template.adminData?.fontStylesList);

                   // Use the one that has content, not just exists (following documentation pattern)
                   const fontStylesList = (template.fontStylesList && template.fontStylesList.length > 0)
                       ? template.fontStylesList
                       : template.adminData?.fontStylesList;
                   console.log('🎯 🔍 Combined fontStylesList:', fontStylesList);
                   console.log('🎯 🔍 fontStylesList is array:', Array.isArray(fontStylesList));
                   console.log('🎯 🔍 fontStylesList length:', fontStylesList?.length);

                   if (fontStylesList && Array.isArray(fontStylesList) && fontStylesList.length > 0) {
                       console.log('🎯 ✅ FONT STYLES FOUND - Loading into window.fontStylesList...', fontStylesList.length, 'styles');
                       window.fontStylesList = fontStylesList;
                       window._templateSpecificFontStylesLoaded = true; // Flag to prevent protection system override
                       console.log('🎯 ✅ window.fontStylesList assigned:', window.fontStylesList);
                       console.log('🎯 ✅ Set _templateSpecificFontStylesLoaded = true');

                       // ULTRA PROTECTION: Store in multiple hidden locations
                       window._fontStylesListBackup = JSON.parse(JSON.stringify(fontStylesList));
                       window.__fontStylesData__ = JSON.parse(JSON.stringify(fontStylesList));

                       // Store in sessionStorage as additional backup
                       sessionStorage.setItem('_fontStylesBackup', JSON.stringify(fontStylesList));

                       // NEW: Use the protection system
                       if (window._fontStylesProtection) {
                           window._fontStylesProtection.protect(fontStylesList);
                           console.log('🎯 🛡️ Font styles data protected using new protection system');
                       }

                       // Store in a hidden DOM element as final backup
                       let hiddenStorage = document.getElementById('_fontStylesStorage');
                       if (!hiddenStorage) {
                           hiddenStorage = document.createElement('div');
                           hiddenStorage.id = '_fontStylesStorage';
                           hiddenStorage.style.display = 'none';
                           hiddenStorage.setAttribute('data-fontstyles', JSON.stringify(fontStylesList));
                           document.body.appendChild(hiddenStorage);
                       }

                       console.log('🎯 🛡️ Created MULTIPLE backup copies of fontStylesList');
                       console.log('🎯 🛡️ Backups stored in: window._fontStylesListBackup, window.__fontStylesData__, sessionStorage, DOM element');

                       // PROTECTION: Make window.fontStylesList non-configurable to prevent overwrites
                       try {
                           Object.defineProperty(window, 'fontStylesList', {
                               value: fontStylesList,
                               writable: true,
                               enumerable: true,
                               configurable: false
                           });
                           console.log('🎯 🛡️ Protected window.fontStylesList from being overwritten');
                       } catch (e) {
                           console.log('🎯 ⚠️ Could not protect window.fontStylesList:', e.message);
                       }

                       // Add a persistent field updater
                       let checkCount = 0;
                       const fieldUpdater = setInterval(() => {
                           checkCount++;
                           const adminFontStylesField = document.getElementById('adminFontStyles');

                           if (!window.fontStylesList || window.fontStylesList.length === 0) {
                               console.log(`🎯 ❌ ALERT: window.fontStylesList was CLEARED at check ${checkCount}!`);
                               console.log('🎯 ❌ Current value:', window.fontStylesList);
                               clearInterval(fieldUpdater);
                           } else if (adminFontStylesField && (!adminFontStylesField.value || adminFontStylesField.value.trim() === '')) {
                               // Field is empty but we have data - populate it
                               let dataToUse = window.fontStylesList;
                               if (!dataToUse || dataToUse.length === 0) {
                                   // Try backup if main is cleared
                                   if (window._fontStylesListBackup && window._fontStylesListBackup.length > 0) {
                                       dataToUse = window._fontStylesListBackup;
                                       window.fontStylesList = window._fontStylesListBackup; // Restore
                                       console.log(`🎯 🔄 PERSISTENT UPDATE ${checkCount}: Restored from backup`);
                                   }
                               }
                               if (dataToUse && dataToUse.length > 0) {
                                   const jsonValue = JSON.stringify(dataToUse, null, 2);
                                   adminFontStylesField.value = jsonValue;
                                   console.log(`🎯 🔄 PERSISTENT UPDATE ${checkCount}: Populated empty field with font styles`);
                               }
                           } else if (adminFontStylesField && adminFontStylesField.value.trim() !== '') {
                               console.log(`🎯 ✅ Field successfully populated at check ${checkCount}`);
                               clearInterval(fieldUpdater);
                           } else if (checkCount >= 20) {
                               console.log(`🎯 ⏰ Stopped checking after ${checkCount} attempts`);
                               clearInterval(fieldUpdater);
                           }
                       }, 500); // Check every 500ms
                       console.log('🎯 ✅ Loaded font styles list:', {
                           'source': template.fontStylesList ? 'top-level' : 'adminData',
                           'styles count': window.fontStylesList.length,
                           'first style preview': window.fontStylesList[0] ? `${window.fontStylesList[0].length} text configs` : 'none',
                           'full data': window.fontStylesList
                       });

                       console.log('🎯 ✅ Calling updateFontStylesStatus...');
                       if (window.updateFontStylesStatus) {
                           window.updateFontStylesStatus();
                           console.log('🎯 ✅ updateFontStylesStatus called');
                       } else {
                           console.log('🎯 ❌ updateFontStylesStatus not available');
                       }

                       console.log('🎯 ✅ Calling updateAdminFontStylesDisplay...');

                       // AGGRESSIVE APPROACH: Try multiple methods to populate the field
                       const populateField = () => {
                           const adminFontStylesField = document.getElementById('adminFontStyles');
                           if (adminFontStylesField && window.fontStylesList && window.fontStylesList.length > 0) {
                               const jsonValue = JSON.stringify(window.fontStylesList, null, 2);
                               adminFontStylesField.value = jsonValue;
                               console.log('🎯 🔧 DIRECT POPULATION - Set adminFontStyles field:', jsonValue);
                               return true;
                           }
                           return false;
                       };

                       // Try immediately
                       if (!populateField()) {
                           console.log('🎯 🔧 Immediate population failed, trying with delays...');

                           // Try after 100ms
                           setTimeout(() => {
                               if (!populateField()) {
                                   // Try after 500ms
                                   setTimeout(() => {
                                       if (!populateField()) {
                                           // Try after 1000ms
                                           setTimeout(() => {
                                               if (!populateField()) {
                                                   console.log('🎯 ❌ All attempts to populate field failed');
                                               }
                                           }, 1000);
                                       }
                                   }, 500);
                               }
                           }, 100);
                       }

                       // Also try the original function approach
                       if (window.updateAdminFontStylesDisplay) {
                           window.updateAdminFontStylesDisplay();
                           console.log('🎯 ✅ updateAdminFontStylesDisplay called');
                       } else {
                           console.log('🎯 ❌ updateAdminFontStylesDisplay not available - will call after script loads');
                           // Try multiple times with increasing delays to ensure the function is available
                           const tryUpdateDisplay = (attempt = 1) => {
                               setTimeout(() => {
                                   if (window.updateAdminFontStylesDisplay) {
                                       window.updateAdminFontStylesDisplay();
                                       console.log(`🎯 ✅ updateAdminFontStylesDisplay called after delay (attempt ${attempt})`);
                                   } else if (attempt < 5) {
                                       console.log(`🎯 🔄 updateAdminFontStylesDisplay still not available, trying again (attempt ${attempt + 1})`);
                                       tryUpdateDisplay(attempt + 1);
                                   } else {
                                       console.log('🎯 ❌ updateAdminFontStylesDisplay never became available after 5 attempts');
                                   }
                               }, attempt * 500); // 500ms, 1000ms, 1500ms, 2000ms, 2500ms
                           };
                           tryUpdateDisplay();
                       }
                   } else {
                       console.log('🎯 ❌ NO FONT STYLES FOUND in template:', {
                           'top-level fontStylesList exists': !!template.fontStylesList,
                           'adminData.fontStylesList exists': !!template.adminData?.fontStylesList,
                           'top-level fontStylesList type': typeof template.fontStylesList,
                           'adminData.fontStylesList type': typeof template.adminData?.fontStylesList,
                           'top-level fontStylesList value': template.fontStylesList,
                           'adminData.fontStylesList value': template.adminData?.fontStylesList,
                           'fontStylesList variable': fontStylesList,
                           'is array check': Array.isArray(fontStylesList)
                       });
                       window.fontStylesList = [];
                       // Still set the flag to indicate we checked for template-specific data
                       window._templateSpecificFontStylesLoaded = true;
                       console.log('🎯 ❌ Set _templateSpecificFontStylesLoaded = true (no data found)');
                   }

                   // Load decor styles list if available (check both top-level and adminData)
                   console.log('🎯 🔍 DECOR STYLES LOADING - Checking for decor styles...');
                   console.log('🎯 🔍 template.decorStylesList:', template.decorStylesList);
                   console.log('🎯 🔍 template.adminData?.decorStylesList:', template.adminData?.decorStylesList);

                   // Use the one that has content, not just exists (following documentation pattern)
                   const decorStylesList = (template.decorStylesList && template.decorStylesList.length > 0)
                       ? template.decorStylesList
                       : template.adminData?.decorStylesList;
                   console.log('🎯 🔍 Combined decorStylesList:', decorStylesList);
                   console.log('🎯 🔍 decorStylesList is array:', Array.isArray(decorStylesList));
                   console.log('🎯 🔍 decorStylesList length:', decorStylesList?.length);

                   if (decorStylesList && Array.isArray(decorStylesList) && decorStylesList.length > 0) {
                       console.log('🎯 ✅ DECOR STYLES FOUND - Loading into window.decorStylesList...', decorStylesList.length, 'styles');
                       window.decorStylesList = decorStylesList;
                       window._templateSpecificDecorStylesLoaded = true; // Flag to prevent protection system override
                       console.log('🎯 ✅ window.decorStylesList assigned:', window.decorStylesList);

                       // Protect the decor styles data
                       if (window._decorStylesProtection) {
                           window._decorStylesProtection.protect(decorStylesList);
                           console.log('🎯 🛡️ Decor styles protected');
                       }

                       // Update admin field if available
                       if (window.updateAdminDecorStylesDisplay) {
                           window.updateAdminDecorStylesDisplay();
                           console.log('🎯 ✅ Called updateAdminDecorStylesDisplay');
                       }
                   } else {
                       console.log('🎯 ❌ NO DECOR STYLES FOUND in template:', {
                           'top-level decorStylesList exists': !!template.decorStylesList,
                           'adminData.decorStylesList exists': !!template.adminData?.decorStylesList,
                           'top-level decorStylesList type': typeof template.decorStylesList,
                           'adminData.decorStylesList type': typeof template.adminData?.decorStylesList,
                           'top-level decorStylesList value': template.decorStylesList,
                           'adminData.decorStylesList value': template.adminData?.decorStylesList,
                           'decorStylesList variable': decorStylesList,
                           'is array check': Array.isArray(decorStylesList)
                       });
                       window.decorStylesList = [];
                       // Still set the flag to indicate we checked for template-specific data
                       window._templateSpecificDecorStylesLoaded = true;
                       console.log('🎯 ❌ Set _templateSpecificDecorStylesLoaded = true (no data found)');
                   }

                   // Restore CSS filter state if available
                   if (template.cssFilterState) {
                       console.log('[SessionStorage] 🎨 Found CSS filter state in template:', template.cssFilterState);
                       // Call the CSS filter restoration function from design-editor.js
                       if (typeof window.restoreCSSFilterState === 'function') {
                           window.restoreCSSFilterState(template.cssFilterState);
                       } else {
                           console.log('[SessionStorage] 🎨 restoreCSSFilterState function not available yet, will retry...');
                           // Retry after a short delay to ensure design-editor.js is loaded
                           setTimeout(() => {
                               if (typeof window.restoreCSSFilterState === 'function') {
                                   window.restoreCSSFilterState(template.cssFilterState);
                               } else {
                                   console.log('[SessionStorage] 🎨 restoreCSSFilterState function still not available');
                               }
                           }, 1000);
                       }
                   } else {
                       console.log('[SessionStorage] 🎨 No CSS filter state found in template, using defaults');
                   }

                   // Also populate other admin fields if available
                   const adminPromptField = document.getElementById('adminPrompt');
                   if (adminPromptField && template.adminData?.prompt) {
                       adminPromptField.value = template.adminData.prompt;
                   }
               } else {
                   console.log('🎯 No template data found in sessionStorage');
                   // Set flags to true when no template is loaded to prevent protection system from overriding
                   window._templateSpecificFontStylesLoaded = true;
                   window._templateSpecificDecorStylesLoaded = true;
                   console.log('🎯 📋 NO TEMPLATE - Set flags to true to prevent protection override');
               }
           } catch (error) {
               console.error('🎯 Error loading template from sessionStorage:', error);
           }
       }









       // Store previous object enhancement answers to avoid repetition
       window.previousAIObjectAnswers = window.previousAIObjectAnswers || {};

       // Enhance AI Object Function
       async function enhanceAIObject(event) {
           // Prevent the click from bubbling up and closing the sidebar
           if (event) {
               event.stopPropagation();
               event.preventDefault();
           }

           console.log('🎯 Enhance AI object called');

           // Get the object input and tone selection
           const objectInput = document.getElementById('aiObjectInput')?.value?.trim();
           const toneSelect = document.getElementById('aiToneSelect');
           const selectedTone = toneSelect?.value || 'Generic';

           if (!objectInput) {
               // Use the same toast system as the design editor
               if (window.showToast) {
                   window.showToast('Please enter an object/subject first', 'error');
               } else {
                   alert('Please enter an object/subject first');
               }
               return;
           }

           // Get the enhance button and update its state
           const enhanceBtn = document.querySelector('.ai-enhance-object-btn');
           if (enhanceBtn) {
               enhanceBtn.disabled = true;
               enhanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
           }

           try {
               // Store the original user input for regeneration
               if (!enhanceBtn.dataset.originalInput) {
                   enhanceBtn.dataset.originalInput = objectInput;
               }
               const originalInput = enhanceBtn.dataset.originalInput;

               // Get previous answers for this original input + tone combination
               const cacheKey = `${originalInput.toLowerCase().trim()}_${selectedTone.toLowerCase()}`;
               const previousAnswers = window.previousAIObjectAnswers[cacheKey] || [];

               // Build the prompt using the same template as other pages
               let prompt = `Give a simple, expressive idea for a t-shirt image: ${originalInput} with a ${selectedTone} tone. Just describe the subject's look, pose, mood or action, something to help to create a punchline, can have a interesting point of view or an exaggerated size of some part — don't need to use all the variables, no text, no full joke. Max 45 characters but can be less, or a lot less.`;

               // Add exclusions if we have previous answers
               if (previousAnswers.length > 0) {
                   prompt += '\n\nIMPORTANT: Do NOT use any of these previous suggestions:\n';
                   previousAnswers.forEach((answer, index) => {
                       prompt += `- "${answer}"\n`;
                   });
                   prompt += '\nCreate a completely different and original description.';
               }

               console.log('🤖 Sending AI object enhancement prompt:', prompt);

               // Call the text generation API (reusing the same endpoint)
               const response = await fetch('/api/generate-texts', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json'
                   },
                   credentials: 'include',
                   body: JSON.stringify({
                       prompt: prompt,
                       objectInput: originalInput,
                       textCount: 1 // We only want one enhanced description
                   })
               });

               if (!response.ok) {
                   const errorData = await response.json();
                   throw new Error(errorData.error || 'Failed to enhance object description');
               }

               const result = await response.json();
               console.log('🤖 AI object enhancement result:', result);

               // Parse the generated description and update the input
               if (result.texts && Array.isArray(result.texts) && result.texts[0]) {
                   const enhancedDescription = result.texts[0].trim();
                   console.log('🎯 Enhanced description:', enhancedDescription);

                   // Update the input field
                   const objectInputField = document.getElementById('aiObjectInput');
                   if (objectInputField) {
                       objectInputField.value = enhancedDescription;
                       // Trigger input event to update any listeners
                       objectInputField.dispatchEvent(new Event('input', { bubbles: true }));
                   }

                   if (window.showToast) {
                       window.showToast('Object description enhanced successfully!', 'success');
                   }

                   // Save the generated description to prevent repetition
                   if (!window.previousAIObjectAnswers[cacheKey]) {
                       window.previousAIObjectAnswers[cacheKey] = [];
                   }
                   window.previousAIObjectAnswers[cacheKey].push(enhancedDescription);

                   // Keep only the last 5 attempts to avoid overly long prompts
                   if (window.previousAIObjectAnswers[cacheKey].length > 5) {
                       window.previousAIObjectAnswers[cacheKey] = window.previousAIObjectAnswers[cacheKey].slice(-5);
                   }

                   console.log('💾 Saved AI object answers for', cacheKey, ':', window.previousAIObjectAnswers[cacheKey]);

                   // Change button to regenerate mode
                   if (enhanceBtn) {
                       enhanceBtn.classList.add('regenerate-mode');
                       enhanceBtn.title = 'Generate different variation';
                   }

               } else {
                   throw new Error('Invalid response format from object enhancement');
               }

           } catch (error) {
               console.error('Error enhancing AI object:', error);
               if (window.showToast) {
                   window.showToast('Failed to enhance object: ' + error.message, 'error');
               } else {
                   alert('Failed to enhance object: ' + error.message);
               }
           } finally {
               // Re-enable the button
               if (enhanceBtn) {
                   enhanceBtn.disabled = false;
                   enhanceBtn.innerHTML = '<i class="fas fa-redo"></i>';
               }
           }
       }

       // Make the function globally available
       window.enhanceAIObject = enhanceAIObject;

       // Store previous text enhancement answers to avoid repetition
       window.previousTextAnswers = window.previousTextAnswers || {};

       // Enhance Selected Text Function
       async function enhanceSelectedText() {
           console.log('🎯 Enhance selected text called');

           // Validate that a text element is selected
           const textInput = document.getElementById('iText');
           if (!textInput || textInput.disabled || !textInput.value.trim()) {
               if (window.showToast) {
                   window.showToast('Please select a text element first', 'error');
               } else {
                   alert('Please select a text element first');
               }
               return;
           }

           // Check if "Replace All Texts" is enabled
           const replaceAllCheckbox = document.getElementById('replaceAllTextsCheckbox');
           const replaceAllTexts = replaceAllCheckbox?.checked || false;

           // Get the current text and tone selection
           const currentText = textInput.value.trim();
           const toneSelect = document.getElementById('textToneSelect');
           const selectedTone = toneSelect?.value || 'Generic';

           // Get object context from Admin tab
           const adminOriginalObjectField = document.getElementById('adminOriginalObject');
           const objectContext = adminOriginalObjectField?.value?.trim() || 'general design';

           // Get the enhance button and update its state
           const enhanceBtn = document.getElementById('enhanceTextBtn');
           if (enhanceBtn) {
               enhanceBtn.disabled = true;
               // Keep icon-only format for square button
               enhanceBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
               if (replaceAllTexts) {
                   enhanceBtn.title = 'Replacing all texts...';
               } else {
                   enhanceBtn.title = 'Generating enhanced text...';
               }
           }

           try {
               if (replaceAllTexts) {
                   // Replace All Texts Mode
                   await replaceAllCanvasTexts(selectedTone, objectContext);
               } else {
                   // Single Text Mode (existing functionality)
                   await replaceSingleText(currentText, selectedTone, objectContext, enhanceBtn);
               }

           } catch (error) {
               console.error('Error enhancing text:', error);
               if (window.showToast) {
                   window.showToast('Failed to enhance text: ' + error.message, 'error');
               } else {
                   alert('Failed to enhance text: ' + error.message);
               }
           } finally {
               // Re-enable the button
               if (enhanceBtn) {
                   enhanceBtn.disabled = false;
                   // Keep icon-only format for square button
                   enhanceBtn.innerHTML = '<i class="fas fa-magic"></i>';
                   enhanceBtn.title = 'Enhance selected text with AI';
               }
           }
       }

       // Function to replace a single text element
       async function replaceSingleText(currentText, selectedTone, objectContext, enhanceBtn) {
           // Measure current text length for target matching
           const targetLength = currentText.length;
           console.log('🎯 Current text:', currentText, 'Length:', targetLength);

           // Store the original text for regeneration
           if (!enhanceBtn.dataset.originalText) {
               enhanceBtn.dataset.originalText = currentText;
           }
           const originalText = enhanceBtn.dataset.originalText;

           // Get previous answers for this original text + tone combination
           const cacheKey = `${originalText.toLowerCase().trim()}_${selectedTone.toLowerCase()}_${objectContext.toLowerCase()}`;
           const previousAnswers = window.previousTextAnswers[cacheKey] || [];

           // Build the prompt using the same template as inspiration.html
           let prompt = `Generate a ${selectedTone.toLowerCase()} text for a t-shirt design about: ${objectContext}.

The text should be:
- ${selectedTone} in tone
- Related to: ${objectContext}
- Approximately ${targetLength} characters long (target: ${targetLength-2} to ${targetLength+2} characters)
- Suitable for t-shirt design
- Creative and engaging

Current text to replace: "${originalText}"

Generate only the text content, no quotes or explanations.`;

           // Add exclusions if we have previous answers
           if (previousAnswers.length > 0) {
               prompt += '\n\nIMPORTANT: Do NOT use any of these previous suggestions:\n';
               previousAnswers.forEach((answer, index) => {
                   prompt += `- "${answer}"\n`;
               });
               prompt += '\nCreate a completely different and original text.';
           }

           console.log('🤖 Sending text enhancement prompt:', prompt);

           // Call the text generation API
           const response = await fetch('/api/generate-texts', {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json'
               },
               credentials: 'include',
               body: JSON.stringify({
                   prompt: prompt,
                   objectInput: objectContext,
                   textCount: 1,
                   targetLength: targetLength
               })
           });

           if (!response.ok) {
               const errorData = await response.json();
               throw new Error(errorData.error || 'Failed to enhance text');
           }

           const result = await response.json();
           console.log('🤖 Text enhancement result:', result);

           // Parse the generated text and update the input
           if (result.texts && Array.isArray(result.texts) && result.texts[0]) {
               let enhancedText = result.texts[0].trim();

               // Check if All Caps should be applied to this text
               const allCapsCheckbox = document.getElementById('iAllCaps');
               const shouldApplyAllCaps = allCapsCheckbox && allCapsCheckbox.checked;

               // Apply All Caps transformation if enabled
               if (shouldApplyAllCaps) {
                   enhancedText = enhancedText.toUpperCase();
                   console.log(`🎯 Applied All Caps to enhanced text: "${enhancedText}"`);
               }

               console.log('🎯 Enhanced text:', enhancedText, 'Length:', enhancedText.length);

               // Update the text input field
               const textInput = document.getElementById('iText');
               textInput.value = enhancedText;

               // Trigger change event to update the canvas text
               textInput.dispatchEvent(new Event('input', { bubbles: true }));
               textInput.dispatchEvent(new Event('change', { bubbles: true }));

               if (window.showToast) {
                   window.showToast(`Text enhanced successfully! (${enhancedText.length} chars)`, 'success');
               }

               // Save the generated text to prevent repetition
               if (!window.previousTextAnswers[cacheKey]) {
                   window.previousTextAnswers[cacheKey] = [];
               }
               window.previousTextAnswers[cacheKey].push(enhancedText);

               // Keep only the last 5 attempts to avoid overly long prompts
               if (window.previousTextAnswers[cacheKey].length > 5) {
                   window.previousTextAnswers[cacheKey] = window.previousTextAnswers[cacheKey].slice(-5);
               }

               console.log('💾 Saved text answers for', cacheKey, ':', window.previousTextAnswers[cacheKey]);

               // Change button to regenerate mode
               if (enhanceBtn) {
                   enhanceBtn.classList.add('regenerate-mode');
                   enhanceBtn.title = 'Generate different variation';
               }

           } else {
               throw new Error('Invalid response format from text enhancement');
           }
       }

       // Function to replace all text elements on the canvas
       async function replaceAllCanvasTexts(selectedTone, objectContext) {
           console.log('🎯 Replace all canvas texts called');

           // Detect which workflow is calling this function
           const isRestyleMode = window.isRestyleMode === true;
           const isReplaceMode = window.isReplaceMode === true;
           const workflowType = isRestyleMode ? 'Restyle' : (isReplaceMode ? 'Replace' : 'Unknown');

           console.log(`🎯 Workflow detected: ${workflowType} mode`);
           console.log(`🎯 Object context: "${objectContext}" (from ${isRestyleMode ? 'Admin tab' : isReplaceMode ? 'Sidebar input' : 'Unknown source'})`);

           // Access canvas objects from the design-editor system
           let allCanvasObjects = [];

           // Check for canvas objects in the design-editor system
           if (window.canvasObjects && Array.isArray(window.canvasObjects)) {
               allCanvasObjects = window.canvasObjects;
               console.log('🎯 Found canvas objects via window.canvasObjects:', allCanvasObjects.length);
           } else {
               console.error('🎯 Cannot access window.canvasObjects');
               throw new Error('Cannot access canvas text elements. Please ensure a text is selected first.');
           }

           // Filter for text objects only
           const allTexts = allCanvasObjects.filter(obj => obj.type === 'text');
           console.log('🎯 Found text objects:', allTexts.length);

           if (allTexts.length === 0) {
               if (window.showToast) {
                   window.showToast('No text elements found on canvas', 'warning');
               }
               return;
           }

           // Get the currently selected text to exclude it from replacement
           const textInput = document.getElementById('iText');
           const currentSelectedText = textInput?.value?.trim();

           // Get the currently selected object index to exclude it
           const selectedIndex = window.selectedObjectIndex || -1;

           // Include ALL texts (including selected one) for replacement
           const textsToReplace = allTexts.filter(textObj => {
               return textObj.text && textObj.text.trim();
           });

           console.log('🎯 Total texts found:', allTexts.length);
           console.log('🎯 Texts to replace (including selected):', textsToReplace.length);

           if (textsToReplace.length === 0) {
               if (window.showToast) {
                   window.showToast('No text elements found to replace', 'warning');
               }
               return;
           }

           try {
               // Build a single prompt for all texts
               let prompt = `Create ${textsToReplace.length} catchy texts in a ${selectedTone.toLowerCase()} tone for a T-shirt design about ${objectContext}.\n\n`;

               // Add each text with its requirements
               textsToReplace.forEach((textObj, index) => {
                   const originalText = textObj.text.trim();
                   const targetLength = originalText.length;
                   prompt += `${index + 1}. Text to replace: "${originalText}" (${targetLength} characters max, Title Case)\n`;
               });

               prompt += `\nRespond with only the ${textsToReplace.length} text contents, no quotes or explanations, one per line.`;

               console.log('🤖 Sending batch text enhancement prompt:', prompt);

               // Call the text generation API with single request
               const response = await fetch('/api/generate-texts', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json'
                   },
                   credentials: 'include',
                   body: JSON.stringify({
                       prompt: prompt,
                       objectInput: objectContext,
                       textCount: textsToReplace.length
                   })
               });

               if (!response.ok) {
                   const errorData = await response.json();
                   throw new Error(errorData.error || 'Failed to enhance texts');
               }

               const result = await response.json();
               console.log('🤖 Batch text enhancement result:', result);

               // Parse the generated texts and update all text objects
               if (result.texts && Array.isArray(result.texts) && result.texts.length >= textsToReplace.length) {
                   let successCount = 0;

                   textsToReplace.forEach((textObj, index) => {
                       if (result.texts[index]) {
                           const originalText = textObj.text.trim();
                           let enhancedText = result.texts[index].trim();

                           // Check if All Caps should be applied to this specific text object
                           console.log(`🎯 Checking All Caps for text "${originalText}": isAllCaps property = ${textObj.isAllCaps}`);

                           // If isAllCaps is not set, detect from current text content
                           if (textObj.isAllCaps === undefined) {
                               const isCurrentlyAllCaps = originalText === originalText.toUpperCase() && originalText !== originalText.toLowerCase();
                               textObj.isAllCaps = isCurrentlyAllCaps;
                               console.log(`🎯 Auto-detected All Caps state for "${originalText}": ${isCurrentlyAllCaps}`);
                           }

                           const shouldApplyAllCaps = textObj.isAllCaps === true;

                           // Apply All Caps transformation if enabled for this text
                           if (shouldApplyAllCaps) {
                               enhancedText = enhancedText.toUpperCase();
                               console.log(`🎯 Applied All Caps to enhanced text (stored state): "${enhancedText}"`);
                           } else {
                               console.log(`🎯 No All Caps applied to text "${originalText}" (isAllCaps: ${textObj.isAllCaps})`);
                           }

                           console.log(`🎯 Enhanced text ${index + 1}: "${originalText}" → "${enhancedText}" (${enhancedText.length} chars)`);

                           // Update the text object directly in the design-editor system
                           textObj.text = enhancedText;
                           successCount++;
                       }
                   });

                   // Update the selected text input if it was one of the replaced texts
                   if (currentSelectedText && selectedIndex >= 0) {
                       const selectedObject = window.canvasObjects[selectedIndex];
                       if (selectedObject && selectedObject.type === 'text') {
                           const textInput = document.getElementById('iText');
                           if (textInput) {
                               textInput.value = selectedObject.text;

                               // Update All Caps checkbox state based on the new text
                               const allCapsCheckbox = document.getElementById('iAllCaps');
                               if (allCapsCheckbox) {
                                   const newText = selectedObject.text;
                                   const isAllCaps = newText === newText.toUpperCase() && newText !== newText.toLowerCase();
                                   allCapsCheckbox.checked = isAllCaps;
                                   console.log(`🎯 Updated All Caps checkbox state: ${isAllCaps} for text: "${newText}"`);
                               }
                           }
                       }
                   }

                   console.log(`🎯 Batch replace completed: ${successCount} texts enhanced`);

                   if (window.showToast) {
                       window.showToast(`All ${successCount} texts enhanced successfully!`, 'success');
                   }
               } else {
                   throw new Error(`Expected ${textsToReplace.length} texts, got ${result.texts?.length || 0}`);
               }

           } catch (error) {
               console.error('Error in batch text enhancement:', error);
               if (window.showToast) {
                   window.showToast('Failed to enhance texts: ' + error.message, 'error');
               }
               throw error;
           }

           // Update All Caps control state for all text enhancement controls
           if (window.updateAllCapsControlState) {
               window.updateAllCapsControlState();
               console.log('🎯 Updated All Caps control state after text replacements');
           }

           // Trigger canvas redraw using the design-editor's update function
           if (window.update && typeof window.update === 'function') {
               window.update();
               console.log('🎯 Canvas updated after text replacements');
           } else {
               console.warn('🎯 Could not find update function to redraw canvas');
           }

           // Note: Success message is already shown in the try block above
           console.log('🎯 Replace all texts completed successfully');
       }

       // Setup All Caps Control
       function setupAllCapsControl() {
           const allCapsCheckbox = document.getElementById('iAllCaps');
           const textInput = document.getElementById('iText');

           if (!allCapsCheckbox || !textInput) return;

           allCapsCheckbox.addEventListener('change', function() {
               if (!textInput.value) return;

               const isChecked = allCapsCheckbox.checked;

               if (isChecked) {
                   // Convert to uppercase
                   textInput.value = textInput.value.toUpperCase();
               } else {
                   // Convert to title case (capitalize first letter of each word)
                   textInput.value = textInput.value.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
               }

               // Store All Caps state in the current text object
               const selectedIndex = window.selectedObjectIndex;
               if (selectedIndex >= 0 && window.canvasObjects && window.canvasObjects[selectedIndex]) {
                   const selectedObject = window.canvasObjects[selectedIndex];
                   if (selectedObject.type === 'text') {
                       selectedObject.isAllCaps = isChecked;
                       console.log(`🎯 Stored All Caps state (${isChecked}) for text object:`, selectedObject.text);
                   }
               }

               // Trigger change event to update the canvas text
               textInput.dispatchEvent(new Event('input', { bubbles: true }));
               textInput.dispatchEvent(new Event('change', { bubbles: true }));

               console.log('All Caps toggled:', isChecked, 'New text:', textInput.value);
           });

           // Update checkbox state when text is selected
           function updateAllCapsState() {
               const currentText = textInput.value;
               console.log(`🎯 updateAllCapsState called with text: "${currentText}"`);

               if (currentText) {
                   // First check if the selected text object has a stored All Caps state
                   const selectedIndex = window.selectedObjectIndex;
                   let isAllCaps = false;

                   if (selectedIndex >= 0 && window.canvasObjects && window.canvasObjects[selectedIndex]) {
                       const selectedObject = window.canvasObjects[selectedIndex];

                       // Always detect from current text content (this is more reliable)
                       isAllCaps = currentText === currentText.toUpperCase() && currentText !== currentText.toLowerCase();

                       // Update the stored state to match the current text
                       if (selectedObject && selectedObject.type === 'text') {
                           const oldStoredState = selectedObject.isAllCaps;
                           selectedObject.isAllCaps = isAllCaps;

                           if (oldStoredState !== isAllCaps) {
                               console.log(`🎯 Updated stored All Caps state from ${oldStoredState} to ${isAllCaps} for text:`, selectedObject.text);
                           } else {
                               console.log(`🎯 All Caps state remains ${isAllCaps} for text:`, selectedObject.text);
                           }
                       }
                   } else {
                       // Fallback: detect from text content
                       isAllCaps = currentText === currentText.toUpperCase() && currentText !== currentText.toLowerCase();
                   }

                   console.log(`🎯 Setting All Caps checkbox to: ${isAllCaps} for text: "${currentText}"`);
                   allCapsCheckbox.checked = isAllCaps;
               } else {
                   console.log(`🎯 No text content, unchecking All Caps checkbox`);
                   allCapsCheckbox.checked = false;
               }
           }

           // Listen for text selection changes
           textInput.addEventListener('input', updateAllCapsState);
           textInput.addEventListener('change', updateAllCapsState);

           // Update All Caps checkbox state based on text selection
           function updateAllCapsControlState() {
               const isTextSelected = textInput && !textInput.disabled && textInput.value.trim();
               allCapsCheckbox.disabled = !isTextSelected;

               if (isTextSelected) {
                   updateAllCapsState();
               } else {
                   allCapsCheckbox.checked = false;
               }
           }

           // Make this function available globally so it can be called when text selection changes
           window.updateAllCapsControlState = updateAllCapsControlState;
       }

       // Make the functions globally available
       window.enhanceSelectedText = enhanceSelectedText;
       window.replaceAllCanvasTexts = replaceAllCanvasTexts;

       // Function to update text enhancement controls state
       function updateTextEnhancementControls() {
           const textInput = document.getElementById('iText');
           const toneSelect = document.getElementById('textToneSelect');
           const enhanceBtn = document.getElementById('enhanceTextBtn');
           const replaceAllCheckbox = document.getElementById('replaceAllTextsCheckbox');

           const isTextSelected = textInput && !textInput.disabled && textInput.value.trim();

           // Enable/disable controls based on text selection
           if (toneSelect) toneSelect.disabled = !isTextSelected;
           if (enhanceBtn) enhanceBtn.disabled = !isTextSelected;
           if (replaceAllCheckbox) replaceAllCheckbox.disabled = !isTextSelected;

           // Update All Caps control state if available
           if (window.updateAllCapsControlState) {
               window.updateAllCapsControlState();
           }

           // Reset button state when text changes
           if (enhanceBtn && isTextSelected) {
               enhanceBtn.classList.remove('regenerate-mode');
               // Keep just the icon for the square button
               enhanceBtn.innerHTML = '<i class="fas fa-magic"></i>';
               enhanceBtn.title = 'Enhance selected text with AI';
               delete enhanceBtn.dataset.originalText;
           }
       }

       // Monitor text input state changes
       function setupTextEnhancementMonitoring() {
           const textInput = document.getElementById('iText');
           if (!textInput) return;

           // Create a MutationObserver to watch for disabled attribute changes
           const observer = new MutationObserver(function(mutations) {
               mutations.forEach(function(mutation) {
                   if (mutation.type === 'attributes' && mutation.attributeName === 'disabled') {
                       updateTextEnhancementControls();
                   }
               });
           });

           // Start observing
           observer.observe(textInput, {
               attributes: true,
               attributeFilter: ['disabled']
           });

           // Also listen for value changes
           textInput.addEventListener('input', updateTextEnhancementControls);
           textInput.addEventListener('change', updateTextEnhancementControls);

           // Initial state update
           updateTextEnhancementControls();

           // Setup All Caps functionality
           setupAllCapsControl();
       }

       // Initialize text enhancement monitoring when DOM is ready
       document.addEventListener('DOMContentLoaded', function() {
           setupTextEnhancementMonitoring();
       });

       // If DOM is already loaded, setup immediately
       if (document.readyState === 'loading') {
           document.addEventListener('DOMContentLoaded', setupTextEnhancementMonitoring);
       } else {
           setupTextEnhancementMonitoring();
       }

       // ===== CSS FILTERS FOR IMAGES =====

       // Filter presets from adjust.html
       const filterPresets = {
           "watercolor": {
               "brightness": 1.3,
               "invert": 0.17,
               "saturate": 2.6,
               "sepia": 0.25
           },
           "faded-photo": {
               "blur": 0.2,
               "brightness": 1.1,
               "hueRotate": 5,
               "opacity": 0.9,
               "saturate": 1.3,
               "sepia": 0.4
           },
           "old-horror": {
               "grayscale": 1,
               "sepia": 0.5,
               "brightness": 1.3,
               "invert": 0.8
           },
           "old-grainy": {
               "grayscale": 0.6,
               "sepia": 0.5,
               "brightness": 1.5
           },
           "fade-out": {
               "brightness": 0.8,
               "hueRotate": 350,
               "saturate": 3,
               "blur": 8,
               "contrast": 0.6
           },
           "mist": {
               "brightness": 0.8,
               "saturate": 0.8
           }
       };

       // Function to apply CSS filters to selected image
       async function applyImageFilters() {
           console.log('🎨 [CSS Filters] ========== applyImageFilters() called ==========');

           // Find the selected object by checking isSelected property
           let selectedObject = null;
           let selectedIndex = -1;

           if (window.canvasObjects) {
               console.log('🎨 [CSS Filters] Checking', window.canvasObjects.length, 'canvas objects for selection');
               for (let i = 0; i < window.canvasObjects.length; i++) {
                   if (window.canvasObjects[i].isSelected) {
                       selectedObject = window.canvasObjects[i];
                       selectedIndex = i;
                       console.log('🎨 [CSS Filters] Found selected object at index', i, 'type:', selectedObject.type);
                       break;
                   }
               }
           } else {
               console.log('🎨 [CSS Filters] ❌ window.canvasObjects is not available');
           }

           console.log('🎨 [CSS Filters] Selected object:', selectedObject ? selectedObject.type : 'none');
           console.log('🎨 [CSS Filters] Selected index:', selectedIndex);

           if (selectedObject && selectedObject.type === 'image') {
                   // Get filter values from controls
                   const filters = {
                       blur: parseFloat(document.getElementById('iImageBlur')?.value || 0),
                       brightness: parseFloat(document.getElementById('iImageBrightness')?.value || 1),
                       contrast: parseFloat(document.getElementById('iImageContrast')?.value || 1),
                       saturate: parseFloat(document.getElementById('iImageSaturation')?.value || 1),
                       hueRotate: parseFloat(document.getElementById('iImageHue')?.value || 0),
                       grayscale: parseFloat(document.getElementById('iImageGrayscale')?.value || 0),
                       sepia: parseFloat(document.getElementById('iImageSepia')?.value || 0),
                       invert: parseFloat(document.getElementById('iImageInvert')?.value || 0)
                   };

                   console.log('🎨 [CSS Filters] Filter values:', filters);

                   // Store filters in the image object
                   selectedObject.cssFilters = filters;

                   // Build CSS filter string
                   const filterParts = [];
                   if (filters.blur > 0) filterParts.push(`blur(${filters.blur}px)`);
                   if (filters.brightness !== 1) filterParts.push(`brightness(${filters.brightness})`);
                   if (filters.contrast !== 1) filterParts.push(`contrast(${filters.contrast})`);
                   if (filters.saturate !== 1) filterParts.push(`saturate(${filters.saturate})`);
                   if (filters.hueRotate > 0) filterParts.push(`hue-rotate(${filters.hueRotate}deg)`);
                   if (filters.grayscale > 0) filterParts.push(`grayscale(${filters.grayscale})`);
                   if (filters.sepia > 0) filterParts.push(`sepia(${filters.sepia})`);
                   if (filters.invert > 0) filterParts.push(`invert(${filters.invert})`);

                   selectedObject.cssFilterString = filterParts.length > 0 ? filterParts.join(' ') : 'none';
                   console.log('🎨 [CSS Filters] Generated filter string:', selectedObject.cssFilterString);

                   // Apply filters using client-side processing (simpler and more reliable)
                   console.log('🎨 [CSS Filters] Applying client-side filters');
                   applyClientSideFilters(selectedObject, filters);

                   // 🎯 CAPTURE CSS FILTER STATE FOR SAVE/LOAD FUNCTIONALITY
                   captureCSSFilterState();

                   // Update canvas
                   if (window.update && typeof window.update === 'function') {
                       console.log('🎨 [CSS Filters] Calling window.update()');
                       window.update();

                       // Force a second update to ensure image is visible (like Reset does)
                       setTimeout(() => {
                           console.log('🎨 [CSS Filters] Forcing second update to ensure visibility');
                           window.update();
                       }, 50);
                   } else {
                       console.log('🎨 [CSS Filters] window.update not available');
                   }
               } else {
                   console.log('🎨 [CSS Filters] No valid image selected');
               }
       }

       // Make applyImageFilters globally accessible for template loading
       window.applyImageFilters = applyImageFilters;

       // Function to capture current CSS filter state into global variable
       function captureCSSFilterState() {
           const filterControls = [
               { id: 'iImageBlur', key: 'blur' },
               { id: 'iImageBrightness', key: 'brightness' },
               { id: 'iImageContrast', key: 'contrast' },
               { id: 'iImageSaturation', key: 'saturate' },
               { id: 'iImageHue', key: 'hueRotate' },
               { id: 'iImageGrayscale', key: 'grayscale' },
               { id: 'iImageSepia', key: 'sepia' },
               { id: 'iImageInvert', key: 'invert' }
           ];

           const cssFilterState = {};

           filterControls.forEach(control => {
               const element = document.getElementById(control.id);
               if (element) {
                   cssFilterState[control.key] = parseFloat(element.value);
               }
           });

           // Update global state
           window.cssFilterState = cssFilterState;

           console.log('🎨 [CSS Filters] Captured filter state to window.cssFilterState:', cssFilterState);
           return cssFilterState;
       }

       // Make captureCSSFilterState globally accessible
       window.captureCSSFilterState = captureCSSFilterState;

       // Function to apply filters server-side for images
       async function applyServerSideFilters(imageObject, filters, imageSource) {
           try {
               console.log('🎨 [CSS Filters] Requesting server-side filter processing...');

               const response = await fetch('/api/apply-image-filters', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json',
                   },
                   body: JSON.stringify({
                       imageUrl: imageSource,
                       filters: filters
                   })
               });

               if (!response.ok) {
                   throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
               }

               const result = await response.json();
               console.log('🎨 [CSS Filters] Server-side filter result:', result);

               if (result.success && result.filteredImageUrl) {
                   // Create new image with filtered version
                   const filteredImg = new Image();
                   filteredImg.crossOrigin = 'anonymous';

                   filteredImg.onload = () => {
                       // Replace the image object's image with the filtered version
                       imageObject.image = filteredImg;
                       imageObject.filteredImageUrl = result.filteredImageUrl;
                       console.log('🎨 [CSS Filters] Filtered image loaded and applied');

                       // Update canvas
                       if (window.update && typeof window.update === 'function') {
                           window.update();
                       }
                   };

                   filteredImg.onerror = () => {
                       console.error('🎨 [CSS Filters] Failed to load filtered image');
                   };

                   filteredImg.src = result.filteredImageUrl;
               }
           } catch (error) {
               console.error('🎨 [CSS Filters] Server-side filter error:', error);
               throw error;
           }
       }

       // Function to apply filters client-side
       function applyClientSideFilters(imageObject, filters) {
           console.log('🎨 [CSS Filters] Applying client-side filters');

           try {
               // Ensure we have the original image stored for non-cumulative filtering
               if (!imageObject.originalImage && imageObject.image) {
                   imageObject.originalImage = imageObject.image;
                   console.log('🎨 [CSS Filters] Stored original image reference for non-cumulative filtering');
               }

               // Store the filter string for canvas rendering (this is used by drawImageObject)
               imageObject.cssFilterString = buildFilterString(filters);
               imageObject.cssFilters = filters;

               console.log('🎨 [CSS Filters] Client-side filters applied:', imageObject.cssFilterString);
               console.log('🎨 [CSS Filters] Filter object stored in image:', imageObject.cssFilters);
               console.log('🎨 [CSS Filters] Original image preserved:', !!imageObject.originalImage);

               // Clear any cached filtered images to force regeneration
               if (imageObject.filteredImageCache) {
                   imageObject.filteredImageCache = {};
                   console.log('🎨 [CSS Filters] Cleared filter cache');
               }

               // Also clear any cached images in the canvas system
               if (imageObject.cachedFilteredImages) {
                   imageObject.cachedFilteredImages = {};
                   console.log('🎨 [CSS Filters] Cleared canvas filter cache');
               }

               // Mark the object as needing a visual update
               imageObject.needsUpdate = true;

               // Force immediate canvas redraw to ensure image is visible
               console.log('🎨 [CSS Filters] Forcing immediate canvas redraw');

           } catch (error) {
               console.error('🎨 [CSS Filters] Client-side filter error:', error);
           }
       }

       // Function to build CSS filter string from filter values
       function buildFilterString(filters) {
           const filterParts = [];
           if (filters.blur > 0) filterParts.push(`blur(${filters.blur}px)`);
           if (filters.brightness !== 1) filterParts.push(`brightness(${filters.brightness})`);
           if (filters.contrast !== 1) filterParts.push(`contrast(${filters.contrast})`);
           if (filters.saturate !== 1) filterParts.push(`saturate(${filters.saturate})`);
           if (filters.hueRotate > 0) filterParts.push(`hue-rotate(${filters.hueRotate}deg)`);
           if (filters.grayscale > 0) filterParts.push(`grayscale(${filters.grayscale})`);
           if (filters.sepia > 0) filterParts.push(`sepia(${filters.sepia})`);
           if (filters.invert > 0) filterParts.push(`invert(${filters.invert})`);

           return filterParts.length > 0 ? filterParts.join(' ') : 'none';
       }

       // Function to update filter value displays
       function updateFilterValueDisplay(filterName, value) {
           // Convert control ID to display ID (e.g., 'iImageBrightness' -> 'vImageBrightness')
           const displayId = filterName.replace('iImage', 'vImage');
           const display = document.getElementById(displayId);

           console.log('🎨 [CSS Filters] Updating display for:', filterName, '-> ID:', displayId, 'value:', value);

           if (display) {
               let displayValue = value;
               switch (filterName) {
                   case 'iImageBlur':
                       displayValue = value + 'px';
                       break;
                   case 'iImageHue':
                       displayValue = value + '°';
                       break;
                   case 'iImageBrightness':
                   case 'iImageContrast':
                   case 'iImageSaturation':
                       displayValue = Math.round(value * 100) + '%';
                       break;
                   case 'iImageGrayscale':
                   case 'iImageSepia':
                   case 'iImageInvert':
                       displayValue = Math.round(value * 100) + '%';
                       break;
               }
               display.textContent = displayValue;
               console.log('🎨 [CSS Filters] Display updated to:', displayValue);
           } else {
               console.log('🎨 [CSS Filters] ❌ Display element not found:', displayId);
           }
       }

       // Make updateFilterValueDisplay globally accessible for template loading
       window.updateFilterValueDisplay = updateFilterValueDisplay;

       // Function to apply filter preset
       function applyFilterPreset(presetName) {
           const preset = filterPresets[presetName];
           if (!preset) return;

           console.log('🎨 [CSS Filters] Applying preset:', presetName, preset);

           // Reset all filters first
           resetImageFilters(false);

           // Apply preset values
           Object.keys(preset).forEach(key => {
               const controlId = `iImage${key.charAt(0).toUpperCase() + key.slice(1)}`;
               const control = document.getElementById(controlId);
               if (control) {
                   control.value = preset[key];
                   updateFilterValueDisplay(controlId, preset[key]);
               }
           });

           applyImageFilters().then(() => {
               // Capture filter state after preset application
               captureCSSFilterState();
           });
       }

       // Function to reset image filters
       function resetImageFilters(updateUI = true) {
           console.log('🎨 [CSS Filters] Resetting image filters, updateUI:', updateUI);

           // Map filter names to actual control IDs
           const filterControlMap = {
               blur: 'iImageBlur',
               brightness: 'iImageBrightness',
               contrast: 'iImageContrast',
               saturate: 'iImageSaturation',  // Note: saturate -> Saturation
               hueRotate: 'iImageHue',        // Note: hueRotate -> Hue
               grayscale: 'iImageGrayscale',
               sepia: 'iImageSepia',
               invert: 'iImageInvert'
           };

           const defaultValues = {
               iImageBlur: 0,
               iImageBrightness: 1,
               iImageContrast: 1,
               iImageSaturation: 1,
               iImageHue: 0,
               iImageGrayscale: 0,
               iImageSepia: 0,
               iImageInvert: 0
           };

           if (updateUI) {
               Object.keys(defaultValues).forEach(controlId => {
                   const control = document.getElementById(controlId);
                   if (control) {
                       control.value = defaultValues[controlId];
                       updateFilterValueDisplay(controlId, defaultValues[controlId]);
                       console.log('🎨 [CSS Filters] Reset control:', controlId, 'to:', defaultValues[controlId]);
                   } else {
                       console.log('🎨 [CSS Filters] ❌ Control not found:', controlId);
                   }
               });

               // Clear preset dropdown selection
               const presetSelect = document.getElementById('filterPresetSelect');
               if (presetSelect) {
                   presetSelect.value = '';
               }
           }

           applyImageFilters().then(() => {
               // Capture filter state after reset
               captureCSSFilterState();
           });
       }

       // Function to save current filter settings as a new preset
       function saveCurrentFilterPreset(presetName) {
           console.log('🎨 [CSS Filters] Saving new preset:', presetName);

           // Get current filter values
           const currentFilters = {};
           const filterControls = [
               { id: 'iImageBlur', key: 'blur', defaultValue: 0 },
               { id: 'iImageBrightness', key: 'brightness', defaultValue: 1 },
               { id: 'iImageContrast', key: 'contrast', defaultValue: 1 },
               { id: 'iImageSaturation', key: 'saturate', defaultValue: 1 },
               { id: 'iImageHue', key: 'hueRotate', defaultValue: 0 },
               { id: 'iImageGrayscale', key: 'grayscale', defaultValue: 0 },
               { id: 'iImageSepia', key: 'sepia', defaultValue: 0 },
               { id: 'iImageInvert', key: 'invert', defaultValue: 0 }
           ];

           // Only save non-default values
           filterControls.forEach(filter => {
               const control = document.getElementById(filter.id);
               if (control) {
                   const value = parseFloat(control.value);
                   if (value !== filter.defaultValue) {
                       currentFilters[filter.key] = value;
                   }
               }
           });

           // Check if any filters are set
           if (Object.keys(currentFilters).length === 0) {
               alert('No filters are currently applied. Please adjust some filters before saving a preset.');
               return;
           }

           // Add to filterPresets object
           const presetKey = presetName.toLowerCase().replace(/\s+/g, '-');
           filterPresets[presetKey] = currentFilters;

           // Save to database for persistence
           savePresetToDatabase(presetKey, presetName, currentFilters);

           // Add to dropdown
           const presetSelect = document.getElementById('filterPresetSelect');
           const deletePresetBtn = document.getElementById('deletePresetBtn');

           if (presetSelect) {
               const option = document.createElement('option');
               option.value = presetName.toLowerCase().replace(/\s+/g, '-');
               option.textContent = presetName;
               presetSelect.appendChild(option);

               // Select the new preset
               presetSelect.value = option.value;

               // Show delete button for all presets
               if (deletePresetBtn) {
                   deletePresetBtn.style.display = 'block';
               }
           }

           // Clear the input field
           const presetNameInput = document.getElementById('newPresetName');
           if (presetNameInput) {
               presetNameInput.value = '';
           }

           console.log('🎨 [CSS Filters] Preset saved:', presetName, currentFilters);
           alert(`Filter preset "${presetName}" saved successfully!`);
       }

       // Function to save preset to database
       async function savePresetToDatabase(presetKey, presetName, filters) {
           try {
               console.log('🎨 [CSS Filters] Saving preset to database:', presetKey);

               const response = await fetch('/api/filter-presets', {
                   method: 'POST',
                   headers: {
                       'Content-Type': 'application/json'
                   },
                   body: JSON.stringify({
                       key: presetKey,
                       name: presetName,
                       filters: filters
                   })
               });

               if (response.ok) {
                   console.log('🎨 [CSS Filters] Preset saved to database successfully');
               } else {
                   console.error('🎨 [CSS Filters] Failed to save preset to database:', response.statusText);
               }
           } catch (error) {
               console.error('🎨 [CSS Filters] Error saving preset to database:', error);
           }
       }

       // Function to delete preset from database
       async function deletePresetFromDatabase(presetKey) {
           try {
               console.log('🎨 [CSS Filters] Deleting preset from database:', presetKey);

               const response = await fetch(`/api/filter-presets/${presetKey}`, {
                   method: 'DELETE'
               });

               if (response.ok) {
                   console.log('🎨 [CSS Filters] Preset deleted from database successfully');
               } else {
                   console.error('🎨 [CSS Filters] Failed to delete preset from database:', response.statusText);
               }
           } catch (error) {
               console.error('🎨 [CSS Filters] Error deleting preset from database:', error);
           }
       }

       // Function to show delete preset confirmation
       function showDeletePresetConfirmation(presetKey, presetDisplayName) {
           console.log('🎨 [CSS Filters] Delete confirmation for:', presetKey, presetDisplayName);

           // Show confirmation dialog
           const confirmed = confirm(`Are you sure you want to delete the preset "${presetDisplayName}"?\n\nThis action cannot be undone.`);

           if (confirmed) {
               deleteFilterPreset(presetKey, presetDisplayName);
           }
       }

       // Function to delete a filter preset
       function deleteFilterPreset(presetKey, presetDisplayName) {
           console.log('🎨 [CSS Filters] Deleting preset:', presetKey);

           // Remove from filterPresets object
           if (filterPresets[presetKey]) {
               delete filterPresets[presetKey];
           }

           // Delete from database
           deletePresetFromDatabase(presetKey);

           // Remove from dropdown
           const presetSelect = document.getElementById('filterPresetSelect');
           const deletePresetBtn = document.getElementById('deletePresetBtn');

           if (presetSelect) {
               const optionToRemove = presetSelect.querySelector(`option[value="${presetKey}"]`);
               if (optionToRemove) {
                   optionToRemove.remove();
               }

               // Reset dropdown selection
               presetSelect.value = '';
           }

           // Hide delete button
           if (deletePresetBtn) {
               deletePresetBtn.style.display = 'none';
           }

           console.log('🎨 [CSS Filters] Preset deleted:', presetKey);
           alert(`Filter preset "${presetDisplayName}" deleted successfully!`);
       }

       // Debounce timer for filter application
       let filterDebounceTimer = null;

       // Setup CSS filter controls
       function setupImageFilterControls() {
           console.log('🎨 [CSS Filters] Setting up image filter controls...');

           // Setup filter sliders
           const filterControls = [
               'iImageBlur', 'iImageBrightness', 'iImageContrast', 'iImageSaturation',
               'iImageHue', 'iImageGrayscale', 'iImageSepia', 'iImageInvert'
           ];

           filterControls.forEach(controlId => {
               const control = document.getElementById(controlId);
               if (control) {
                   console.log('🎨 [CSS Filters] Found control:', controlId);

                   // Add input event listener with debouncing
                   control.addEventListener('input', (e) => {
                       console.log('🎨 [CSS Filters] ⚡ INPUT EVENT TRIGGERED on:', controlId, 'value:', e.target.value);

                       // Update display immediately for responsive UI
                       updateFilterValueDisplay(controlId, parseFloat(e.target.value));

                       // Clear existing timer
                       if (filterDebounceTimer) {
                           clearTimeout(filterDebounceTimer);
                       }

                       // Set new timer to apply filters after 500ms of no input
                       filterDebounceTimer = setTimeout(() => {
                           console.log('🎨 [CSS Filters] 🕐 Debounced filter application triggered for:', controlId);

                           // Show processing indicator
                           showFilterProcessingIndicator(true);

                           applyImageFilters().finally(() => {
                               // Hide processing indicator
                               showFilterProcessingIndicator(false);
                               // Capture filter state after application
                               captureCSSFilterState();
                           });
                       }, 500);

                       // Clear preset dropdown selection when manually adjusting
                       const presetSelect = document.getElementById('filterPresetSelect');
                       if (presetSelect) {
                           presetSelect.value = '';
                       }
                   });

                   // Add change event for immediate application when user releases slider
                   control.addEventListener('change', (e) => {
                       console.log('🎨 [CSS Filters] ⚡ CHANGE EVENT on:', controlId, 'value:', e.target.value);

                       // Clear debounce timer and apply immediately
                       if (filterDebounceTimer) {
                           clearTimeout(filterDebounceTimer);
                           filterDebounceTimer = null;
                       }

                       updateFilterValueDisplay(controlId, parseFloat(e.target.value));
                       applyImageFilters().then(() => {
                           // Capture filter state after application
                           captureCSSFilterState();
                       });
                   });
               } else {
                   console.log('🎨 [CSS Filters] Control not found:', controlId);
               }
           });

           // Setup preset dropdown
           const presetSelect = document.getElementById('filterPresetSelect');
           const deletePresetBtn = document.getElementById('deletePresetBtn');

           if (presetSelect) {
               presetSelect.addEventListener('change', (e) => {
                   const presetName = e.target.value;

                   console.log('🎨 [CSS Filters] Preset selected:', presetName);

                   // Show/hide delete button based on selection
                   if (deletePresetBtn) {
                       // Allow deletion of any preset (you can now delete built-in presets too)
                       const canDelete = presetName && presetName !== '';

                       console.log('🎨 [CSS Filters] Can delete preset?', canDelete, 'Selected:', presetName);
                       console.log('🎨 [CSS Filters] Setting delete button display to:', canDelete ? 'block' : 'none');

                       deletePresetBtn.style.display = canDelete ? 'block' : 'none';
                   }

                   if (presetName) {
                       applyFilterPreset(presetName);
                   }
               });
           }

           // Setup delete preset button
           if (deletePresetBtn) {
               deletePresetBtn.addEventListener('click', () => {
                   const selectedPreset = presetSelect.value;
                   const selectedOption = presetSelect.options[presetSelect.selectedIndex];

                   if (selectedPreset && selectedOption) {
                       showDeletePresetConfirmation(selectedPreset, selectedOption.textContent);
                   }
               });
           }

           // Setup save preset functionality
           const savePresetBtn = document.getElementById('saveFilterPresetBtn');
           const presetNameInput = document.getElementById('newPresetName');

           if (savePresetBtn && presetNameInput) {
               savePresetBtn.addEventListener('click', () => {
                   const presetName = presetNameInput.value.trim();
                   if (!presetName) {
                       alert('Please enter a preset name');
                       return;
                   }

                   saveCurrentFilterPreset(presetName);
               });
           }

           // Setup reset button
           const resetBtn = document.getElementById('resetImageFiltersBtn');
           if (resetBtn) {
               resetBtn.addEventListener('click', () => {
                   resetImageFilters(true);
               });
           }

           // Setup test button
           const testBtn = document.getElementById('testImageFiltersBtn');
           if (testBtn) {
               testBtn.addEventListener('click', () => {
                   console.log('🎨 [CSS Filters] Test button clicked');

                   // Find the selected object by checking isSelected property
                   let selectedObject = null;
                   if (window.canvasObjects) {
                       for (let i = 0; i < window.canvasObjects.length; i++) {
                           if (window.canvasObjects[i].isSelected) {
                               selectedObject = window.canvasObjects[i];
                               break;
                           }
                       }
                   }

                   if (selectedObject && selectedObject.type === 'image') {
                           // Apply grayscale filter directly
                           selectedObject.cssFilterString = 'grayscale(1)';
                           selectedObject.cssFilters = { grayscale: 1 };
                           console.log('🎨 [CSS Filters] Applied test grayscale filter');

                           // Update UI
                           const grayscaleSlider = document.getElementById('iImageGrayscale');
                           if (grayscaleSlider) {
                               grayscaleSlider.value = 1;
                               const valueDisplay = document.getElementById('vImageGrayscale');
                               if (valueDisplay) valueDisplay.textContent = '100%';
                           }

                           // Force update
                           if (window.update && typeof window.update === 'function') {
                               window.update();
                           }
                       }
               });
           }

           // Setup debug button
           const debugBtn = document.getElementById('debugImageFiltersBtn');
           if (debugBtn) {
               debugBtn.addEventListener('click', () => {
                   console.log('🔍 [DEBUG] Debug button clicked');
                   console.log('🔍 [DEBUG] window.selectedObjectIndex:', window.selectedObjectIndex);
                   console.log('🔍 [DEBUG] window.canvasObjects:', window.canvasObjects);

                   if (window.canvasObjects && window.canvasObjects.length > 0) {
                       console.log('🔍 [DEBUG] All canvas objects:');
                       window.canvasObjects.forEach((obj, index) => {
                           console.log(`🔍 [DEBUG] Object ${index}:`, {
                               type: obj.type,
                               id: obj.id,
                               isSelected: obj.isSelected,
                               cssFilters: obj.cssFilters,
                               cssFilterString: obj.cssFilterString
                           });
                       });
                   }

                   // Find the selected object by checking isSelected property
                   let selectedObject = null;
                   if (window.canvasObjects) {
                       for (let i = 0; i < window.canvasObjects.length; i++) {
                           if (window.canvasObjects[i].isSelected) {
                               selectedObject = window.canvasObjects[i];
                               break;
                           }
                       }
                   }

                   if (selectedObject) {
                       console.log('🔍 [DEBUG] Selected object details:', {
                           type: selectedObject.type,
                           id: selectedObject.id,
                           imageUrl: selectedObject.imageUrl,
                           cssFilters: selectedObject.cssFilters,
                           cssFilterString: selectedObject.cssFilterString
                       });
                   } else {
                       console.log('🔍 [DEBUG] No object selected');
                   }
               });
           }
       }

       // Function to show/hide filter processing indicator
       function showFilterProcessingIndicator(show) {
           const indicator = document.getElementById('filter-processing-indicator');
           if (indicator) {
               indicator.style.display = show ? 'block' : 'none';
           } else if (show) {
               // Create indicator if it doesn't exist
               const newIndicator = document.createElement('div');
               newIndicator.id = 'filter-processing-indicator';
               newIndicator.innerHTML = '🎨 Processing filters...';
               newIndicator.style.cssText = `
                   position: fixed;
                   top: 10px;
                   right: 10px;
                   background: rgba(0,0,0,0.8);
                   color: white;
                   padding: 8px 12px;
                   border-radius: 4px;
                   font-size: 12px;
                   z-index: 10000;
                   pointer-events: none;
               `;
               document.body.appendChild(newIndicator);
           }
       }

       // Add hover effects for save button
       document.addEventListener('DOMContentLoaded', function() {
           const saveBtn = document.getElementById('saveFilterPresetBtn');
           if (saveBtn) {
               saveBtn.addEventListener('mouseenter', function() {
                   this.style.backgroundColor = '#218838';
               });
               saveBtn.addEventListener('mouseleave', function() {
                   this.style.backgroundColor = '#28a745';
               });
           }
       });

       // Function to load presets from database
       async function loadPresetsFromDatabase() {
           try {
               console.log('🎨 [CSS Filters] Loading presets from database...');

               const response = await fetch('/api/filter-presets');
               if (response.ok) {
                   const presets = await response.json();

                   // Add custom presets to filterPresets object
                   presets.forEach(preset => {
                       filterPresets[preset.key] = preset.filters;

                       // Add to dropdown if not already there
                       const presetSelect = document.getElementById('filterPresetSelect');
                       if (presetSelect && !presetSelect.querySelector(`option[value="${preset.key}"]`)) {
                           const option = document.createElement('option');
                           option.value = preset.key;
                           option.textContent = preset.name;
                           presetSelect.appendChild(option);
                       }
                   });

                   console.log('🎨 [CSS Filters] Loaded', presets.length, 'custom presets from database');
               } else {
                   console.log('🎨 [CSS Filters] No custom presets found in database');
               }
           } catch (error) {
               console.error('🎨 [CSS Filters] Error loading presets from database:', error);
           }
       }

       // Initialize CSS filter controls when DOM is ready
       document.addEventListener('DOMContentLoaded', () => {
           console.log('🎨 [CSS Filters] DOMContentLoaded event fired, setting up filters...');
           setTimeout(() => {
               setupImageFilterControls();
               loadPresetsFromDatabase();
           }, 1000); // Delay to ensure all elements are ready
       });

       if (document.readyState !== 'loading') {
           console.log('🎨 [CSS Filters] DOM already loaded, setting up filters with delay...');
           setTimeout(setupImageFilterControls, 1000);
       }

       // ===== USER-FRIENDLY TEXT EDITING WITHOUT SELECTION =====

       // Function to get or create a text object to work with
       function getOrCreateTextObject() {
           // Check if we have canvas objects
           if (!window.canvasObjects || !Array.isArray(window.canvasObjects)) {
               console.log('🎯 No canvas objects available');
               return null;
           }

           // Find all text objects
           const textObjects = window.canvasObjects.filter(obj => obj.type === 'text');

           if (textObjects.length === 0) {
               // No text objects exist, create a new one
               console.log('🎯 No text objects found, creating new text');

               // Create a new text object
               const newText = {
                   id: Date.now(),
                   type: 'text',
                   text: 'Sample Text',
                   x: 400, // Center of typical canvas
                   y: 300,
                   fontSize: 48,
                   fontFamily: 'Arial',
                   color: '#000000',
                   rotation: 0
               };

               // Add to canvas objects
               window.canvasObjects.push(newText);

               // Update canvas
               if (window.update && typeof window.update === 'function') {
                   window.update();
               }

               console.log('🎯 Created new text object:', newText);
               return newText;
           } else {
               // Use the first text object
               console.log('🎯 Using first text object:', textObjects[0]);
               return textObjects[0];
           }
       }

       // Function to apply changes to text object
       function applyTextChanges(textObj, changes) {
           if (!textObj) return;

           // Apply changes
           Object.keys(changes).forEach(key => {
               if (changes[key] !== undefined) {
                   textObj[key] = changes[key];
                   console.log(`🎯 Applied ${key}: ${changes[key]} to text "${textObj.text}"`);
               }
           });

           // Update canvas
           if (window.update && typeof window.update === 'function') {
               window.update();
           }
       }

       // Setup text input to work without selection
       function setupUserFriendlyTextEditing() {
           const textInput = document.getElementById('iText');
           const colorInput = document.getElementById('iTextColor');
           const rotationInput = document.getElementById('iTextRotation');
           const rotationValue = document.getElementById('vTextRotation');

           if (textInput) {
               textInput.addEventListener('input', function() {
                   const newText = this.value.trim();
                   if (newText) {
                       const textObj = getOrCreateTextObject();
                       if (textObj) {
                           applyTextChanges(textObj, { text: newText });
                       }
                   }
               });
           }

           if (colorInput) {
               colorInput.addEventListener('input', function() {
                   const newColor = this.value;
                   const textObj = getOrCreateTextObject();
                   if (textObj) {
                       applyTextChanges(textObj, { color: newColor });
                   }
               });
           }

           if (rotationInput && rotationValue) {
               rotationInput.addEventListener('input', function() {
                   const newRotation = parseInt(this.value);
                   rotationValue.textContent = newRotation + '°';

                   const textObj = getOrCreateTextObject();
                   if (textObj) {
                       applyTextChanges(textObj, { rotation: newRotation });
                   }
               });
           }

           console.log('🎯 User-friendly text editing setup complete');
       }

       // Initialize user-friendly text editing
       document.addEventListener('DOMContentLoaded', setupUserFriendlyTextEditing);
       if (document.readyState !== 'loading') {
           setupUserFriendlyTextEditing();
       }

       // ===== ENSURE FONT/DECOR BUTTONS ARE ALWAYS ENABLED =====

       function ensureButtonsEnabled() {
           const changeFontsBtn = document.getElementById('changeFontsBtn');
           const changeDecorBtn = document.getElementById('changeDecorBtn');
           const changeColorsBtn = document.getElementById('changeColorsBtn');
           const saveFontStyleBtn = document.getElementById('saveFontStyleBtn');
           const saveDecorStyleBtn = document.getElementById('saveDecorStyleBtn');

           // Force enable these buttons
           if (changeFontsBtn) {
               changeFontsBtn.disabled = false;
               changeFontsBtn.removeAttribute('disabled');
               console.log('🎯 Enabled changeFontsBtn');
           }

           if (changeDecorBtn) {
               changeDecorBtn.disabled = false;
               changeDecorBtn.removeAttribute('disabled');
               console.log('🎯 Enabled changeDecorBtn');
           }

           if (changeColorsBtn) {
               changeColorsBtn.disabled = false;
               changeColorsBtn.removeAttribute('disabled');
               console.log('🎯 Enabled changeColorsBtn');
           }

           if (saveFontStyleBtn) {
               saveFontStyleBtn.disabled = false;
               saveFontStyleBtn.removeAttribute('disabled');
               console.log('🎯 Enabled saveFontStyleBtn');
           }

           if (saveDecorStyleBtn) {
               saveDecorStyleBtn.disabled = false;
               saveDecorStyleBtn.removeAttribute('disabled');
               console.log('🎯 Enabled saveDecorStyleBtn');
           }
       }

       // Flag to track if template/project is fully loaded
       let isFullyLoaded = false;
       let buttonProtectionInterval = null;

       // Run immediately and periodically to prevent other code from disabling buttons
       function setupButtonProtection() {
           ensureButtonsEnabled();

           // Only run interval if not fully loaded
           if (!isFullyLoaded) {
               // Run less frequently to avoid performance issues
               buttonProtectionInterval = setInterval(() => {
                   if (isFullyLoaded) {
                       clearInterval(buttonProtectionInterval);
                       console.log('🎯 Button protection stopped - template/project fully loaded');
                       return;
                   }
                   ensureButtonsEnabled();
               }, 5000); // Every 5 seconds instead of continuous

               // Also run when template loads (reduced frequency)
               setTimeout(ensureButtonsEnabled, 2000);
               setTimeout(() => {
                   if (!isFullyLoaded) {
                       ensureButtonsEnabled();
                   }
               }, 10000);
           }

           console.log('🎯 Button protection setup complete (conditional)');
       }

       // Function to mark as fully loaded and stop protection loops
       function markAsFullyLoaded() {
           isFullyLoaded = true;
           window.isFullyLoaded = true; // Also set global flag
           if (buttonProtectionInterval) {
               clearInterval(buttonProtectionInterval);
               buttonProtectionInterval = null;
           }
           console.log('🎯 System marked as fully loaded - stopping protection loops');
       }

       // Expose globally for access from other scripts
       window.markAsFullyLoaded = markAsFullyLoaded;

       // Initialize button protection
       document.addEventListener('DOMContentLoaded', setupButtonProtection);
       if (document.readyState !== 'loading') {
           setupButtonProtection();
       }

       // ===== MUTATION OBSERVER TO PREVENT BUTTON DISABLING =====

       function setupMutationObserver() {
           const targetButtons = ['changeFontsBtn', 'changeDecorBtn', 'saveFontStyleBtn', 'saveDecorStyleBtn'];

           targetButtons.forEach(buttonId => {
               const button = document.getElementById(buttonId);
               if (button) {
                   // Create observer for this button
                   const observer = new MutationObserver(function(mutations) {
                       mutations.forEach(function(mutation) {
                           if (mutation.type === 'attributes' && mutation.attributeName === 'disabled') {
                               // If button gets disabled, immediately re-enable it
                               if (button.disabled) {
                                   console.log(`🎯 Detected ${buttonId} was disabled, re-enabling...`);
                                   button.disabled = false;
                                   button.removeAttribute('disabled');
                               }
                           }
                       });
                   });

                   // Start observing
                   observer.observe(button, {
                       attributes: true,
                       attributeFilter: ['disabled']
                   });

                   console.log(`🎯 Mutation observer setup for ${buttonId}`);
               }
           });
       }

       // Setup mutation observer after DOM is ready
       document.addEventListener('DOMContentLoaded', function() {
           setTimeout(setupMutationObserver, 1000); // Delay to ensure buttons exist
       });
       if (document.readyState !== 'loading') {
           setTimeout(setupMutationObserver, 1000);
       }

       // AI Generator functionality is handled by left-menu.js
    </script>
<script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr@1.9.1/dist/pickr.min.js"></script>
<collection-modal></collection-modal>
<project-modal></project-modal>
<toast-notification></toast-notification>
</body>
</html>
